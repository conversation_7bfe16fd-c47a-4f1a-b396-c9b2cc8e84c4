from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class Tenant(BaseModel):
    id: str
    name: str
    status: str = "Active"
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: str
