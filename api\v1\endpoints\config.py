from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, UploadFile, File
from typing import List, Dict, Any, Optional
import yaml
import json
from pymongo import MongoClient
from datetime import datetime
from app.db.mongo_db import get_collection
from app.core.config import settings

router = APIRouter()

@router.post("/upload")
async def upload_configuration(
    file: UploadFile = File(...),
    tenant_id: str = Query(..., description="Tenant ID"),
    description: str = Query(None, description="Configuration description"),
    user_id: str = Query(..., description="User uploading the configuration")
):
    """
    Upload a YAML configuration file.
    """
    try:
        # Read file content
        content = await file.read()
        
        # Parse YAML content
        if file.filename.endswith('.yaml') or file.filename.endswith('.yml'):
            config = yaml.safe_load(content)
        elif file.filename.endswith('.json'):
            config = json.loads(content)
        else:
            raise HTTPException(status_code=400, detail="Unsupported file format. Only YAML and JSON are supported.")
        
        # Generate version ID
        version_id = f"v_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Store in MongoDB
        collection = get_collection("configuration_versions")
        document = {
            "tenant_id": tenant_id,
            "version_id": version_id,
            "name": file.filename,
            "description": description or f"Uploaded on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "status": "Draft",
            "version_number": "1.0",
            "content": config,
            "created_at": datetime.now(),
            "created_by": user_id
        }
        
        result = collection.insert_one(document)
        
        return {
            "message": "Configuration uploaded successfully",
            "version_id": version_id,
            "document_id": str(result.inserted_id)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading configuration: {str(e)}")

@router.get("/versions")
async def list_configuration_versions(
    tenant_id: str = Query(..., description="Tenant ID"),
    status: str = Query(None, description="Filter by status"),
    limit: int = Query(10, description="Maximum number of versions to return"),
    skip: int = Query(0, description="Number of versions to skip")
):
    """
    List configuration versions for a tenant.
    """
    try:
        collection = get_collection("configuration_versions")
        
        # Build filter
        filter_query = {"tenant_id": tenant_id}
        if status:
            filter_query["status"] = status
        
        # Execute query
        cursor = collection.find(filter_query).sort("created_at", -1).skip(skip).limit(limit)
        
        # Convert to list and format
        versions = []
        for doc in cursor:
            versions.append({
                "version_id": doc["version_id"],
                "name": doc["name"],
                "description": doc["description"],
                "status": doc["status"],
                "version_number": doc["version_number"],
                "created_at": doc["created_at"],
                "created_by": doc["created_by"]
            })
        
        return versions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing configuration versions: {str(e)}")

@router.get("/versions/{version_id}")
async def get_configuration_version(
    version_id: str = Path(..., description="Version ID"),
    tenant_id: str = Query(..., description="Tenant ID")
):
    """
    Get a specific configuration version.
    """
    try:
        collection = get_collection("configuration_versions")
        
        # Find configuration
        doc = collection.find_one({"tenant_id": tenant_id, "version_id": version_id})
        
        if not doc:
            raise HTTPException(status_code=404, detail=f"Configuration version {version_id} not found")
        
        # Format response
        result = {
            "version_id": doc["version_id"],
            "name": doc["name"],
            "description": doc["description"],
            "status": doc["status"],
            "version_number": doc["version_number"],
            "content": doc["content"],
            "created_at": doc["created_at"],
            "created_by": doc["created_by"]
        }
        
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving configuration version: {str(e)}")

@router.post("/versions/{version_id}/deploy")
async def deploy_configuration(
    version_id: str = Path(..., description="Version ID"),
    tenant_id: str = Query(..., description="Tenant ID"),
    user_id: str = Body(..., description="User deploying the configuration")
):
    """
    Deploy a configuration version to production.
    """
    try:
        collection = get_collection("configuration_versions")
        
        # Find configuration
        doc = collection.find_one({"tenant_id": tenant_id, "version_id": version_id})
        
        if not doc:
            raise HTTPException(status_code=404, detail=f"Configuration version {version_id} not found")
        
        # Check if already deployed
        if doc["status"] == "Production":
            return {"message": f"Configuration version {version_id} is already deployed"}
        
        # Update status
        collection.update_one(
            {"tenant_id": tenant_id, "version_id": version_id},
            {
                "$set": {
                    "status": "Production",
                    "deployed_at": datetime.now(),
                    "deployed_by": user_id
                }
            }
        )
        
        # TODO: Process configuration components (tenants, entities, objectives, etc.)
        # This would extract components from the configuration and store them in their respective collections
        
        return {"message": f"Configuration version {version_id} deployed successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deploying configuration: {str(e)}")
