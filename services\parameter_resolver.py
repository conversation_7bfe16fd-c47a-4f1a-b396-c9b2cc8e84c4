from sqlalchemy.sql import text
from typing import Dict, Any, List, Optional, Union, Set
import copy
import json
import re
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("parameter_resolver")

class ParameterResolver:
    """
    Enhanced parameter resolver that supports:
    - New input types (system_dependent, information)
    - Dependencies between inputs
    - Proper filtering of information-only fields
    - Improved contextual lookup with better error handling
    """

    def __init__(self, db_session=None):
        self.db_session = db_session
        self._cache = {}  # Cache for expensive lookups
        self.resolution_depth = 0  # Track recursion depth to prevent infinite loops
        self.MAX_RESOLUTION_DEPTH = 10
        
    def resolve_parameters(self, raw_params: Dict[str, Any], context_data: Dict[str, Any], 
                          exclude_types: Set[str] = None) -> Dict[str, Any]:
        """
        Resolve all parameters with a unified strategy.
        
        Args:
            raw_params: The raw parameters from function definition
            context_data: The execution context with all available variables
            exclude_types: Set of input types to exclude from resolution (e.g., 'information')
            
        Returns:
            Resolved parameters with all references replaced by actual values
        """
        self.resolution_depth = 0  # Reset recursion counter
        
        if not raw_params:
            return {}
        
        # Log the raw parameters for debugging
        print(f"DEBUG: Parameter resolver received raw parameters: {raw_params}")
        
        # Deep copy to avoid modifying the original
        resolved_params = copy.deepcopy(raw_params)
        
        # Process each parameter recursively
        for key, value in resolved_params.items():
            print(f"DEBUG: Resolving parameter '{key}' with value '{value}' (type: {type(value)})")
            resolved_value = self._resolve_value(value, context_data, exclude_types)
            print(f"DEBUG: Parameter '{key}' resolved from '{value}' to '{resolved_value}' (type: {type(resolved_value)})")
            resolved_params[key] = resolved_value
        
        # Log the final resolved parameters
        print(f"DEBUG: Final resolved parameters: {resolved_params}")
            
        return resolved_params

    def _lookup_value_from_context(self, key: str, context_data: Dict[str, Any], 
                                  exclude_types: Set[str] = None) -> Any:
        """
        Enhanced lookup that handles various reference patterns and respects excluded types.
        
        Args:
            key: The key to look up
            context_data: The context data dictionary
            exclude_types: Set of input types to exclude
            
        Returns:
            The value from context, or the original key if not found
        """
        print(f"DEBUG: _lookup_value_from_context called with key='{key}' (type: {type(key)})")
        
        # Check if we should skip this key based on type
        if exclude_types and self._get_input_type(key, context_data) in exclude_types:
            print(f"DEBUG: Skipping excluded type for key: {key}")
            return key
        
        # Try direct lookup
        if key in context_data:
            print(f"DEBUG: Direct lookup successful for key '{key}': {context_data[key]}")
            return context_data[key]
        
        # Try lowercase
        key_lower = key.lower()
        if key_lower in context_data:
            print(f"DEBUG: Lowercase lookup successful for key '{key_lower}': {context_data[key_lower]}")
            return context_data[key_lower]
        
        # Try uppercase
        key_upper = key.upper()
        if key_upper in context_data:
            print(f"DEBUG: Uppercase lookup successful for key '{key_upper}': {context_data[key_upper]}")
            return context_data[key_upper]
        
        # Try with "at" prefix if key is numeric
        if key.isdigit() and f"at{key}" in context_data:
            print(f"DEBUG: Numeric with 'at' prefix lookup successful for key 'at{key}': {context_data[f'at{key}']}")
            return context_data[f"at{key}"]
        
        # Try with "in" prefix if key is numeric
        if key.isdigit() and f"in{key}" in context_data:
            print(f"DEBUG: Numeric with 'in' prefix lookup successful for key 'in{key}': {context_data[f'in{key}']}")
            return context_data[f"in{key}"]
        
        # Try extracting numbers from "in" prefix
        in_match = re.match(r'^in(\d+)$', key)
        if in_match:
            extracted = in_match.group(1)
            print(f"DEBUG: Extracted '{extracted}' from 'in' prefix in key '{key}'")
            if extracted in context_data:
                print(f"DEBUG: Found extracted value '{extracted}' in context: {context_data[extracted]}")
                return context_data[extracted]
        
        # Try extracting numbers from "at" prefix
        at_match = re.match(r'^at(\d+)$', key)
        if at_match:
            extracted = at_match.group(1)
            print(f"DEBUG: Extracted '{extracted}' from 'at' prefix in key '{key}'")
            if extracted in context_data:
                print(f"DEBUG: Found extracted value '{extracted}' in context: {context_data[extracted]}")
                return context_data[extracted]
            # CRITICAL FIX: Don't extract numbers from attribute IDs
            # This is likely causing "at031" to be converted to 1
            print(f"DEBUG: SKIPPING extraction of '{extracted}' from 'at' prefix - this would cause attribute IDs to be converted to numbers")
            # Return the original key instead of trying to extract the number
            return key
        
        # Try removing spaces and looking up
        no_spaces = key.replace(" ", "")
        if no_spaces in context_data:
            return context_data[no_spaces]
        
        # Try a case-insensitive search for display names
        for k, v in context_data.items():
            if isinstance(k, str) and k.lower() == key_lower:
                return v
        
        # Handle dependencies - try to find dependent values
        dependent_value = self._resolve_dependent_value(key, context_data)
        if dependent_value is not None:
            return dependent_value
        
        # Handle input IDs by checking the database
        if re.match(r'^in\d+$', key) and self.db_session:
            try:
                value = self._lookup_input_from_database(key)
                if value is not None:
                    return value
            except Exception as e:
                logger.error(f"Error resolving input ID {key} from database: {str(e)}")
        
        # Not found after all attempts
        logger.warning(f"⚠️ Reference '{key}' not found in context data")
        logger.debug(f"⚠️ Available keys: {list(context_data.keys())[:10]} (showing first 10)")
        return key
    
    def _get_input_type(self, key: str, context_data: Dict[str, Any]) -> Optional[str]:
        """
        Determine the input type for a given key.
        
        Args:
            key: The key to check
            context_data: The context data dictionary
            
        Returns:
            The input type if found, None otherwise
        """
        # Check for explicit type information in context_data
        type_key = f"{key}_type"
        if type_key in context_data:
            return context_data[type_key]
            
        # Check for type in the metadata
        metadata_key = f"{key}_metadata"
        if metadata_key in context_data:
            metadata = context_data[metadata_key]
            if isinstance(metadata, dict) and "source_type" in metadata:
                return metadata["source_type"]
            elif isinstance(metadata, str):
                try:
                    parsed = json.loads(metadata)
                    if isinstance(parsed, dict) and "source_type" in parsed:
                        return parsed["source_type"]
                except (json.JSONDecodeError, TypeError):
                    pass
        
        # If we have a database connection, check there
        if self.db_session and (re.match(r'^in\d+$', key) or re.match(r'^at\d+$', key)):
            try:
                # Try to get the source type from the database
                query = """
                SELECT source_type 
                FROM workflow_runtime.lo_input_items 
                WHERE id = :input_id OR SPLIT_PART(slot_id, '.', 2) = :attr_id
                """
                
                params = {
                    "input_id": key.replace("in", "") if key.startswith("in") else None,
                    "attr_id": key if key.startswith("at") else None
                }
                
                result = self.db_session.execute(text(query), params).fetchone()
                if result:
                    return result[0]
            except Exception as e:
                logger.error(f"Error getting input type for {key}: {str(e)}")
        
        return None
    
    def _resolve_dependent_value(self, key: str, context_data: Dict[str, Any]) -> Optional[Any]:
        """
        Attempt to resolve a value based on dependencies.
        
        Args:
            key: The key to resolve
            context_data: The context data dictionary
            
        Returns:
            The resolved value or None if not found
        """
        # Check if we have dependency information
        dependencies_key = f"{key}_dependencies"
        if dependencies_key in context_data:
            dependencies = context_data[dependencies_key]
            
            # Parse dependencies if needed
            if isinstance(dependencies, str):
                try:
                    dependencies = json.loads(dependencies)
                except (json.JSONDecodeError, TypeError):
                    logger.warning(f"Could not parse dependencies for {key}: {dependencies}")
                    return None
            
            # Check if all dependencies are satisfied
            all_satisfied = True
            dependency_values = {}
            
            for dep in dependencies:
                if dep in context_data:
                    dependency_values[dep] = context_data[dep]
                else:
                    all_satisfied = False
                    break
            
            if all_satisfied and key.startswith("in") and self.db_session:
                # Try to calculate the dependent value using nested functions
                try:
                    # Find the nested function for this input
                    query = """
                    SELECT function_name, parameters
                    FROM workflow_runtime.lo_nested_functions
                    WHERE input_contextual_id LIKE :pattern
                    """
                    
                    pattern = f"%.{key}"
                    result = self.db_session.execute(text(query), {"pattern": pattern}).fetchone()
                    
                    if result:
                        func_name = result[0]
                        params = json.loads(result[1]) if isinstance(result[1], str) else result[1] or {}
                        
                        # Merge with dependency values
                        for param_key, param_value in params.items():
                            if isinstance(param_value, str) and param_value.startswith("${") and param_value.endswith("}"):
                                dep_key = param_value[2:-1]
                                if dep_key in dependency_values:
                                    params[param_key] = dependency_values[dep_key]
                        
                        # Import function repository here to avoid circular imports
                        from app.services.function_repository import function_repository
                        
                        # Execute the function with resolved parameters
                        return function_repository.auto_execute(func_name, self.db_session, **params)
                except Exception as e:
                    logger.error(f"Error resolving dependent value for {key}: {str(e)}")
        
        return None
    
    def _lookup_input_from_database(self, input_id: str) -> Optional[Any]:
        """
        Look up an input value from the database.
        
        Args:
            input_id: The input ID to look up
            
        Returns:
            The input value if found, None otherwise
        """
        if not self.db_session:
            return None
            
        # Check cache first
        cache_key = f"input_lookup_{input_id}"
        if cache_key in self._cache:
            return self._cache[cache_key]
            
        try:
            # First try matching on input ID
            numeric_id = input_id.replace("in", "")
            query = """
            SELECT a.attribute_id, a.display_name, i.input_value
            FROM workflow_runtime.lo_input_items items
            JOIN workflow_runtime.entity_attributes a ON SPLIT_PART(items.slot_id, '.', 2) = a.attribute_id
            JOIN workflow_runtime.lo_input_execution i ON i.input_contextual_id = items.contextual_id
            WHERE items.id = :input_id OR SPLIT_PART(items.contextual_id, '.', 3) = :input_name
            ORDER BY i.created_at DESC
            LIMIT 1
            """
            
            result = self.db_session.execute(text(query), {
                "input_id": numeric_id,
                "input_name": input_id
            }).fetchone()
                
            if result:
                attr_id, display_name, input_value = result
                
                # Parse the value if it's a string
                if isinstance(input_value, str):
                    try:
                        value = json.loads(input_value)
                    except (json.JSONDecodeError, TypeError):
                        value = input_value
                else:
                    value = input_value
                
                # Cache the result
                self._cache[cache_key] = value
                return value
                
        except Exception as e:
            logger.error(f"Error looking up input {input_id} from database: {str(e)}")
            
        return None
        
    def _resolve_value(self, value: Any, context_data: Dict[str, Any], 
                      exclude_types: Set[str] = None) -> Any:
        """
        Recursively resolve a single value with improved handling for complex types.
        
        Args:
            value: The value to resolve
            context_data: The context data dictionary
            exclude_types: Set of input types to exclude
            
        Returns:
            The resolved value
        """
        # Check recursion depth
        self.resolution_depth += 1
        if self.resolution_depth > self.MAX_RESOLUTION_DEPTH:
            logger.warning(f"❌ Maximum resolution depth exceeded ({self.MAX_RESOLUTION_DEPTH}). Possible circular reference.")
            self.resolution_depth -= 1
            return value
            
        try:
            # Handle different value types
            if isinstance(value, dict):
                result = {k: self._resolve_value(v, context_data, exclude_types) for k, v in value.items()}
            elif isinstance(value, list):
                result = [self._resolve_value(item, context_data, exclude_types) for item in value]
            elif isinstance(value, str):
                # 1. Check if this is an explicit reference ${variable_name}
                if value.startswith("${") and value.endswith("}"):
                    var_name = value[2:-1].strip()
                    result = self._lookup_value_from_context(var_name, context_data, exclude_types)
                    
                # 2. Check if this is an input ID pattern (in001, in002, etc.)
                elif re.match(r'^in\d+$', value):
                    result = self._lookup_value_from_context(value, context_data, exclude_types)
                    
                # 3. Check if this matches a display name or attribute ID
                elif value in context_data:
                    result = context_data[value]
                elif value.lower() in context_data:
                    result = context_data[value.lower()]
                    
                # 4. Handle template strings with embedded references
                elif "${" in value:
                    result = self._resolve_template_string(value, context_data, exclude_types)
                    
                # Regular string, no resolution needed
                else:
                    result = value
            else:
                # Non-string value, no resolution needed
                result = value
                
            self.resolution_depth -= 1
            return result
            
        except Exception as e:
            logger.error(f"Error resolving value: {str(e)}")
            self.resolution_depth -= 1
            return value
            
    def _resolve_template_string(self, template: str, context_data: Dict[str, Any],
                                exclude_types: Set[str] = None) -> str:
        """
        Resolve all ${var} references in a template string.
        
        Args:
            template: The template string
            context_data: The context data dictionary
            exclude_types: Set of input types to exclude
            
        Returns:
            The resolved template string
        """
        pattern = re.compile(r'\$\{([^}]+)\}')
        
        def replacer(match):
            var_name = match.group(1).strip()
            result = self._lookup_value_from_context(var_name, context_data, exclude_types)
            # Convert to string if needed
            return str(result) if result is not None else ""
            
        return pattern.sub(replacer, template)
        
    def validate_inputs(self, inputs: Dict[str, Any], rules: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Validate inputs against rules.
        
        Args:
            inputs: Dictionary of input values
            rules: Dictionary of validation rules by input ID
            
        Returns:
            List of validation errors if any
        """
        errors = []
        
        for input_id, input_rules in rules.items():
            value = inputs.get(input_id)
            
            for rule in input_rules:
                rule_type = rule.get("rule_type")
                
                # Skip validation for information-only fields
                input_type = self._get_input_type(input_id, inputs)
                if input_type == "information":
                    continue
                    
                if rule_type == "validate_required" and (value is None or value == ""):
                    errors.append({
                        "input_id": input_id,
                        "rule": rule.get("rule"),
                        "message": rule.get("error_message", "This field is required")
                    })
                elif rule_type == "enum_check" and value is not None:
                    allowed_values = rule.get("allowed_values", [])
                    if isinstance(allowed_values, str):
                        try:
                            allowed_values = json.loads(allowed_values)
                        except (json.JSONDecodeError, TypeError):
                            pass
                            
                    if not isinstance(allowed_values, list):
                        allowed_values = [allowed_values]
                        
                    if str(value).lower() not in [str(v).lower() for v in allowed_values]:
                        errors.append({
                            "input_id": input_id,
                            "rule": rule.get("rule"),
                            "message": rule.get("error_message", f"Value must be one of: {', '.join(allowed_values)}")
                        })
                        
                # Handle custom validation functions
                elif rule_type and "validation_method" in rule:
                    try:
                        # Import function repository here to avoid circular imports
                        from app.services.function_repository import function_repository
                        
                        method_name = rule["validation_method"]
                        
                        # Prepare parameters
                        params = {k: v for k, v in rule.items() if k not in ["rule_type", "validation_method", "rule", "error_message"]}
                        params["value"] = value
                        
                        # Execute validation function
                        result = function_repository.auto_execute(method_name, self.db_session, **params)
                        
                        if isinstance(result, dict) and not result.get("is_valid", True):
                            errors.append({
                                "input_id": input_id,
                                "rule": rule.get("rule"),
                                "message": result.get("message") or rule.get("error_message", "Validation failed")
                            })
                    except Exception as e:
                        logger.error(f"Error executing validation method for {input_id}: {str(e)}")
                        errors.append({
                            "input_id": input_id,
                            "rule": rule.get("rule"),
                            "message": f"Validation error: {str(e)}"
                        })
        
        return errors
    
    def filter_inputs_by_type(self, inputs: Dict[str, Any], types_to_exclude: Set[str]) -> Dict[str, Any]:
        """
        Filter inputs by type, excluding specified types.
        
        Args:
            inputs: Dictionary of input values
            types_to_exclude: Set of input types to exclude
            
        Returns:
            Filtered inputs dictionary
        """
        filtered = {}
        
        for input_id, value in inputs.items():
            # Get the input type
            input_type = self._get_input_type(input_id, inputs)
            
            # Include the input if its type is not in the excluded set
            if input_type not in types_to_exclude:
                filtered[input_id] = value
                
        return filtered
    
    def _get_display_name_for_attribute(self, attribute_id: str) -> Optional[str]:
        """Map attribute IDs to display names using database query."""
        if not self.db_session:
            return None
            
        # Check cache first
        cache_key = f"display_name_{attribute_id}"
        if cache_key in self._cache:
            return self._cache[cache_key]
            
        try:
            query = """
            SELECT display_name
            FROM workflow_runtime.entity_attributes
            WHERE attribute_id = :attribute_id
            """
            result = self.db_session.execute(text(query), {"attribute_id": attribute_id}).fetchone()
            
            if result:
                self._cache[cache_key] = result[0]
                return result[0]
                
            return None
        except Exception as e:
            logger.error(f"Error retrieving display name: {str(e)}")
            return None
