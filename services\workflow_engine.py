"""
Workflow Engine Module

This module provides functions for the workflow engine to check permissions
based on both roles and specific users.
"""

from typing import Dict, Any, List, Optional, Tuple
import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text


# Add to workflow_engine.py or similar file

def check_user_permissions(user_id, objective_id):
    '''
    Check if a user has permissions to access a specific objective.
    This function checks both role-based and user-specific permissions.
    
    Args:
        user_id: The ID of the user
        objective_id: The ID of the objective
        
    Returns:
        Tuple of (has_permission, permission_type, rights)
    '''
    # Step 1: Get user's email
    user_email = get_user_email(user_id)
    if not user_email:
        return False, None, []
    
    # Step 2: Get user's roles
    user_roles = get_user_roles(user_id)
    
    # Step 3: Check objective's agent stack
    agent_stack = get_objective_agent_stack(objective_id)
    if not agent_stack:
        return False, None, []
    
    # Step 4: Check user-specific permissions first
    for agent in agent_stack:
        role = agent.get("role", "")
        rights = agent.get("rights", [])
        users = agent.get("users", [])
        
        # Check if user is explicitly listed
        for user in users:
            if isinstance(user, str) and user == user_email:
                return True, "user_specific", rights
            elif isinstance(user, dict) and user.get("email") == user_email:
                return True, "user_specific", user.get("rights", rights)
    
    # Step 5: Check role-based permissions
    for agent in agent_stack:
        role = agent.get("role", "")
        rights = agent.get("rights", [])
        users = agent.get("users", [])
        
        # If users list is empty, all users with this role have these rights
        if not users and role in user_roles:
            return True, "role_based", rights
    
    # No permissions found
    return False, None, []

def get_user_email(user_id):
    '''Get a user's email by ID.'''
    # In a real implementation, this would query the database
    # For this example, we'll just return a placeholder
    return f"{user_id}@example.com"

def get_user_roles(user_id):
    '''Get a user's roles by ID.'''
    # In a real implementation, this would query the database
    # For this example, we'll just return a placeholder
    return ["r001", "r002"]

def get_objective_agent_stack(objective_id):
    '''Get an objective's agent stack by ID.'''
    # In a real implementation, this would query the database or parse the YAML
    # For this example, we'll just return a placeholder
    return [
        {
            "role": "r001",
            "rights": ["Execute", "Read"],
            "users": []
        },
        {
            "role": "r002",
            "rights": ["Read"],
            "users": ["<EMAIL>"]
        }
    ]
