"""
Organizational Models for v2 API

This module contains Pydantic models for organizational hierarchy and user profiles.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from .models import UserRegistrationResponse


class OrganizationalLevel(str, Enum):
    """Organizational hierarchy levels"""
    executive = "executive"
    senior_management = "senior_management"
    middle_management = "middle_management"
    team_lead = "team_lead"
    senior = "senior"
    mid_level = "mid_level"
    junior = "junior"
    intern = "intern"


class PermissionType(str, Enum):
    """Permission types for granular access control"""
    entity = "entity"
    attribute = "attribute"
    book = "book"
    chapter = "chapter"
    global_objective = "global_objective"
    local_objective = "local_objective"


class PermissionAction(str, Enum):
    """Available permission actions"""
    create = "create"
    read = "read"
    update = "update"
    delete = "delete"


class DepartmentResponse(BaseModel):
    """Department information response model"""
    department_id: str = Field(..., description="Department ID")
    name: str = Field(..., description="Department name")
    description: Optional[str] = Field(None, description="Department description")
    head_user_id: Optional[str] = Field(None, description="Department head user ID")
    parent_department_id: Optional[str] = Field(None, description="Parent department ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")

    class Config:
        schema_extra = {
            "example": {
                "department_id": "DEPT001",
                "name": "Engineering",
                "description": "Software Engineering Department",
                "head_user_id": "U1",
                "parent_department_id": None,
                "created_at": "2025-05-24T15:30:00Z",
                "updated_at": "2025-05-24T15:30:00Z"
            }
        }


class TeamResponse(BaseModel):
    """Team information response model"""
    team_id: str = Field(..., description="Team ID")
    name: str = Field(..., description="Team name")
    description: Optional[str] = Field(None, description="Team description")
    department_id: str = Field(..., description="Department ID")
    team_lead_user_id: Optional[str] = Field(None, description="Team lead user ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")

    class Config:
        schema_extra = {
            "example": {
                "team_id": "TEAM001",
                "name": "Backend Development",
                "description": "Backend API Development Team",
                "department_id": "DEPT001",
                "team_lead_user_id": "U2",
                "created_at": "2025-05-24T15:30:00Z",
                "updated_at": "2025-05-24T15:30:00Z"
            }
        }


class UserTeamMembershipResponse(BaseModel):
    """User team membership response model"""
    team_id: str = Field(..., description="Team ID")
    team_name: str = Field(..., description="Team name")
    is_primary_team: bool = Field(..., description="Whether this is the user's primary team")
    joined_at: Optional[datetime] = Field(None, description="When user joined the team")

    class Config:
        schema_extra = {
            "example": {
                "team_id": "TEAM001",
                "team_name": "Backend Development",
                "is_primary_team": True,
                "joined_at": "2025-05-24T15:30:00Z"
            }
        }


class SystemPermissionResponse(BaseModel):
    """System permission response model"""
    permission_id: str = Field(..., description="Permission ID")
    permission_name: str = Field(..., description="Permission name")
    permission_type: PermissionType = Field(..., description="Permission type")
    resource_identifier: str = Field(..., description="Specific resource identifier")
    available_actions: List[PermissionAction] = Field(..., description="Available actions for this permission")
    granted_actions: List[PermissionAction] = Field(..., description="Actions granted to the user")
    description: Optional[str] = Field(None, description="Permission description")

    class Config:
        schema_extra = {
            "example": {
                "permission_id": "PERM_ENTITY_E1",
                "permission_name": "E1 LeaveApplication Entity",
                "permission_type": "entity",
                "resource_identifier": "e1_leaveapplication",
                "available_actions": ["create", "read", "update", "delete"],
                "granted_actions": ["create", "read"],
                "description": "Access to E1 LeaveApplication entity"
            }
        }


class RoleKPIResponse(BaseModel):
    """Role KPI response model"""
    kpi_id: str = Field(..., description="KPI ID")
    role_id: str = Field(..., description="Role ID")
    name: str = Field(..., description="KPI name")
    description: Optional[str] = Field(None, description="KPI description")
    formula: Optional[str] = Field(None, description="KPI calculation formula")
    target: Optional[str] = Field(None, description="Target value")
    measurement_frequency: Optional[str] = Field(None, description="Measurement frequency")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")

    class Config:
        schema_extra = {
            "example": {
                "kpi_id": "1",
                "role_id": "R001",
                "name": "User Satisfaction Score",
                "description": "Average user satisfaction rating",
                "formula": "AVG(satisfaction_ratings)",
                "target": "4.5",
                "measurement_frequency": "monthly",
                "created_at": "2025-05-24T15:30:00Z"
            }
        }


class OrganizationalHierarchyResponse(BaseModel):
    """Organizational hierarchy response model"""
    user_id: str = Field(..., description="User ID")
    reports_to_user_id: Optional[str] = Field(None, description="Manager user ID")
    organizational_level: Optional[OrganizationalLevel] = Field(None, description="Organizational level")
    direct_reports: List[str] = Field(default_factory=list, description="List of direct report user IDs")
    hierarchy_path: List[str] = Field(default_factory=list, description="Path from root to current user")

    class Config:
        schema_extra = {
            "example": {
                "user_id": "U5",
                "reports_to_user_id": "U2",
                "organizational_level": "mid_level",
                "direct_reports": ["U6", "U7"],
                "hierarchy_path": ["U1", "U2", "U5"]
            }
        }


class UserProfileResponse(BaseModel):
    """Enhanced user profile response with organizational context"""
    # Basic user information
    user_id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    email: str = Field(..., description="Email address")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    status: str = Field(..., description="User status")
    roles: List[str] = Field(default_factory=list, description="User roles")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")
    disabled: bool = Field(default=False, description="Whether user is disabled")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    
    # Organizational information
    department: Optional[DepartmentResponse] = Field(None, description="Department information")
    team_memberships: List[UserTeamMembershipResponse] = Field(default_factory=list, description="Team memberships")
    organizational_hierarchy: Optional[OrganizationalHierarchyResponse] = Field(None, description="Organizational hierarchy")
    
    # Permission information
    entity_permissions: List[SystemPermissionResponse] = Field(default_factory=list, description="Entity-level permissions")
    attribute_permissions: List[SystemPermissionResponse] = Field(default_factory=list, description="Attribute-level permissions")
    global_objective_permissions: List[SystemPermissionResponse] = Field(default_factory=list, description="Global objective permissions")
    local_objective_permissions: List[SystemPermissionResponse] = Field(default_factory=list, description="Local objective permissions")
    book_permissions: List[SystemPermissionResponse] = Field(default_factory=list, description="Book permissions")
    chapter_permissions: List[SystemPermissionResponse] = Field(default_factory=list, description="Chapter permissions")
    
    # Role-based information
    role_kpis: List[RoleKPIResponse] = Field(default_factory=list, description="Role KPIs")
    
    # Team and department context
    team_colleagues: List[str] = Field(default_factory=list, description="Team colleague user IDs across all teams")
    department_colleagues: List[str] = Field(default_factory=list, description="Department colleague user IDs")

    class Config:
        schema_extra = {
            "example": {
                "user_id": "U5",
                "username": "john.doe",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "status": "active",
                "roles": ["Manager"],
                "tenant_id": "t001",
                "disabled": False,
                "created_at": "2025-05-24T15:30:00Z",
                "department": {
                    "department_id": "DEPT001",
                    "name": "Engineering",
                    "description": "Software Engineering Department"
                },
                "team_memberships": [
                    {
                        "team_id": "TEAM001",
                        "team_name": "Backend Development",
                        "is_primary_team": True,
                        "joined_at": "2025-05-24T15:30:00Z"
                    }
                ],
                "organizational_hierarchy": {
                    "user_id": "U5",
                    "reports_to_user_id": "U2",
                    "organizational_level": "mid_level",
                    "direct_reports": ["U6", "U7"],
                    "hierarchy_path": ["U1", "U2", "U5"]
                },
                "entity_permissions": [
                    {
                        "permission_id": "PERM_ENTITY_E1",
                        "permission_name": "E1 LeaveApplication Entity",
                        "permission_type": "entity",
                        "resource_identifier": "e1_leaveapplication",
                        "available_actions": ["create", "read", "update", "delete"],
                        "granted_actions": ["create", "read"]
                    }
                ],
                "role_kpis": [
                    {
                        "kpi_id": "1",
                        "role_id": "R002",
                        "name": "Team Performance",
                        "target": "85%"
                    }
                ],
                "team_colleagues": ["U6", "U7", "U8"],
                "department_colleagues": ["U3", "U4", "U9", "U10"]
            }
        }


class UserProfileUpdateRequest(BaseModel):
    """User profile update request model"""
    first_name: Optional[str] = Field(None, min_length=1, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, min_length=1, max_length=100, description="Last name")
    department_id: Optional[str] = Field(None, description="Department ID")
    reports_to_user_id: Optional[str] = Field(None, description="Manager user ID")
    organizational_level: Optional[OrganizationalLevel] = Field(None, description="Organizational level")

    class Config:
        schema_extra = {
            "example": {
                "first_name": "John",
                "last_name": "Doe",
                "department_id": "DEPT001",
                "reports_to_user_id": "U2",
                "organizational_level": "mid_level"
            }
        }


class UserTeamMembershipRequest(BaseModel):
    """User team membership request model"""
    team_id: str = Field(..., description="Team ID")
    is_primary_team: bool = Field(default=False, description="Whether this should be the user's primary team")

    class Config:
        schema_extra = {
            "example": {
                "team_id": "TEAM001",
                "is_primary_team": True
            }
        }


class EnhancedLoginResponse(BaseModel):
    """Enhanced login response with organizational context"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_at: int = Field(..., description="Token expiration timestamp")
    user: UserProfileResponse = Field(..., description="Enhanced user profile information")

    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_at": 1716563118,
                "user": {
                    "user_id": "U5",
                    "username": "john.doe",
                    "email": "<EMAIL>",
                    "first_name": "John",
                    "last_name": "Doe",
                    "status": "active",
                    "roles": ["Manager"],
                    "department": {
                        "department_id": "DEPT001",
                        "name": "Engineering"
                    },
                    "entity_permissions": [
                        {
                            "permission_id": "PERM_ENTITY_E1",
                            "permission_name": "E1 LeaveApplication Entity",
                            "permission_type": "entity",
                            "resource_identifier": "e1_leaveapplication",
                            "granted_actions": ["create", "read"]
                        }
                    ]
                }
            }
        }


class OrganizationalTreeNode(BaseModel):
    """Organizational tree node model"""
    user_id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    organizational_level: Optional[OrganizationalLevel] = Field(None, description="Organizational level")
    department_id: Optional[str] = Field(None, description="Department ID")
    primary_team_id: Optional[str] = Field(None, description="Primary team ID")
    children: List['OrganizationalTreeNode'] = Field(default_factory=list, description="Direct reports")

    class Config:
        schema_extra = {
            "example": {
                "user_id": "U1",
                "username": "ceo",
                "first_name": "Jane",
                "last_name": "Smith",
                "organizational_level": "executive",
                "department_id": None,
                "primary_team_id": None,
                "children": [
                    {
                        "user_id": "U2",
                        "username": "eng.manager",
                        "first_name": "Bob",
                        "last_name": "Johnson",
                        "organizational_level": "senior_management",
                        "department_id": "DEPT001",
                        "children": []
                    }
                ]
            }
        }


class OrganizationalTreeResponse(BaseModel):
    """Organizational tree response model"""
    root_nodes: List[OrganizationalTreeNode] = Field(..., description="Root level users")
    total_users: int = Field(..., description="Total number of users in the tree")
    max_depth: int = Field(..., description="Maximum depth of the organizational tree")

    class Config:
        schema_extra = {
            "example": {
                "root_nodes": [
                    {
                        "user_id": "U1",
                        "username": "ceo",
                        "first_name": "Jane",
                        "last_name": "Smith",
                        "organizational_level": "executive",
                        "children": []
                    }
                ],
                "total_users": 25,
                "max_depth": 4
            }
        }


# Update forward reference for recursive model
OrganizationalTreeNode.model_rebuild()


class DepartmentListResponse(BaseModel):
    """Department list response model"""
    departments: List[DepartmentResponse] = Field(..., description="List of departments")
    total_count: int = Field(..., description="Total number of departments")

    class Config:
        schema_extra = {
            "example": {
                "departments": [
                    {
                        "department_id": "DEPT001",
                        "name": "Engineering",
                        "description": "Software Engineering Department"
                    }
                ],
                "total_count": 5
            }
        }


class TeamListResponse(BaseModel):
    """Team list response model"""
    teams: List[TeamResponse] = Field(..., description="List of teams")
    total_count: int = Field(..., description="Total number of teams")

    class Config:
        schema_extra = {
            "example": {
                "teams": [
                    {
                        "team_id": "TEAM001",
                        "name": "Backend Development",
                        "description": "Backend API Development Team",
                        "department_id": "DEPT001"
                    }
                ],
                "total_count": 8
            }
        }


class PermissionCheckRequest(BaseModel):
    """Permission check request model"""
    user_id: str = Field(..., description="User ID to check permissions for")
    permission_type: PermissionType = Field(..., description="Type of permission to check")
    resource_identifier: str = Field(..., description="Specific resource identifier")
    action: PermissionAction = Field(..., description="Action to check permission for")

    class Config:
        schema_extra = {
            "example": {
                "user_id": "U5",
                "permission_type": "entity",
                "resource_identifier": "e1_leaveapplication",
                "action": "create"
            }
        }


class PermissionCheckResponse(BaseModel):
    """Permission check response model"""
    user_id: str = Field(..., description="User ID")
    permission_type: PermissionType = Field(..., description="Permission type")
    resource_identifier: str = Field(..., description="Resource identifier")
    action: PermissionAction = Field(..., description="Action")
    has_permission: bool = Field(..., description="Whether user has the permission")
    granted_via: Optional[str] = Field(None, description="How permission was granted (role, user_override)")
    conditions: Optional[Dict[str, Any]] = Field(None, description="Any conditions that apply")

    class Config:
        schema_extra = {
            "example": {
                "user_id": "U5",
                "permission_type": "entity",
                "resource_identifier": "e1_leaveapplication",
                "action": "create",
                "has_permission": True,
                "granted_via": "role:R003",
                "conditions": {"own_records_only": True}
            }
        }
