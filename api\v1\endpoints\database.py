from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from app.database.postgres import get_db
from app.services.database_utils import create_runtime_tables

router = APIRouter()

@router.post("/create-tables")
def create_runtime_tables_api(db: Session = Depends(get_db)):
    """
    API to create database tables dynamically from entity definitions.
    """
    try:
        create_runtime_tables(db)
        return {"status": "success", "message": "Runtime tables created successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))