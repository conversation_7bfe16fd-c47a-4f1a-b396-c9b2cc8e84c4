"""
V2 System Functions Adapter

Adapter to integrate V2 system functions with existing LO execution service.
This provides a bridge between the V1 and V2 function execution systems.
"""

import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from .function_executor import execute_v2_function
from .models import SystemFunctionOutput, ExecutionStatus

logger = logging.getLogger(__name__)

class V2FunctionAdapter:
    """
    Adapter class to integrate V2 system functions with existing workflow execution.
    
    This class provides a seamless transition from V1 to V2 function execution
    while maintaining backward compatibility.
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def execute_nested_function_v2(
        self,
        function_name: str,
        input_values: Dict[str, Any],
        go_id: Optional[str] = None,
        lo_id: Optional[str] = None,
        user_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
        instance_id: Optional[str] = None,
        primary_entity_id: Optional[str] = None,
        primary_attribute_id: Optional[str] = None,
        nested_function_id: Optional[str] = None
    ) -> Any:
        """
        Execute a nested function using V2 system functions.
        
        This method provides a clean interface for LO execution service
        to call V2 functions with standardized parameters.
        """
        self.logger.info(f"🔧 V2 ADAPTER: Executing nested function {function_name}")
        self.logger.info(f"📋 Context: GO={go_id}, LO={lo_id}, Entity={primary_entity_id}")
        self.logger.info(f"📋 Input Values: {input_values}")
        
        try:
            # Get additional parameters from nested function configuration if available
            enhanced_input_values = input_values.copy()
            
            # Add entity and attribute to input values if available from context
            if primary_entity_id:
                enhanced_input_values["entity"] = primary_entity_id
            if primary_attribute_id:
                enhanced_input_values["attribute"] = primary_attribute_id
            
            self.logger.info(f"📋 Using input stack values with entity/attribute context")
            
            self.logger.info(f"📋 Enhanced Input Values: {enhanced_input_values}")
            
            # Execute using V2 function executor
            result = execute_v2_function(
                function_name=function_name,
                db_session=self.db,
                input_values=enhanced_input_values,
                go_id=go_id,
                lo_id=lo_id,
                user_id=user_id,
                tenant_id=tenant_id,
                instance_id=instance_id,
                primary_entity_id=primary_entity_id,
                primary_attribute_id=primary_attribute_id,
                execution_context={"nested_function_id": nested_function_id} if nested_function_id else None
            )
            
            # Handle the result
            if result.status == ExecutionStatus.SUCCESS:
                self.logger.info(f"✅ V2 function executed successfully: {result.result}")
                return result.result
            else:
                self.logger.error(f"❌ V2 function failed: {result.error_message}")
                # Return None for failed executions (maintains compatibility)
                return None
                
        except Exception as e:
            self.logger.error(f"💥 V2 adapter execution failed: {str(e)}")
            return None
    
    def execute_system_function_v2(
        self,
        function_name: str,
        entity_id: Optional[str] = None,
        attribute_id: Optional[str] = None,
        **kwargs
    ) -> Any:
        """
        Execute a system function using V2 with entity/attribute context.
        
        This method is designed for system function calls that need
        entity and attribute context (like generate_id).
        """
        self.logger.info(f"🔧 V2 ADAPTER: Executing system function {function_name}")
        self.logger.info(f"📋 Entity: {entity_id}, Attribute: {attribute_id}")
        self.logger.info(f"📋 Additional params: {kwargs}")
        
        try:
            # Prepare input values
            input_values = kwargs.copy()
            
            # Add entity and attribute if provided
            if entity_id:
                input_values["entity"] = entity_id
            if attribute_id:
                input_values["attribute"] = attribute_id
            
            # Execute using V2 function executor
            result = execute_v2_function(
                function_name=function_name,
                db_session=self.db,
                input_values=input_values,
                primary_entity_id=entity_id,
                primary_attribute_id=attribute_id
            )
            
            # Handle the result
            if result.status == ExecutionStatus.SUCCESS:
                self.logger.info(f"✅ V2 system function executed successfully: {result.result}")
                return result.result
            else:
                self.logger.error(f"❌ V2 system function failed: {result.error_message}")
                return None
                
        except Exception as e:
            self.logger.error(f"💥 V2 adapter system function execution failed: {str(e)}")
            return None
    
    def is_v2_function_available(self, function_name: str) -> bool:
        """
        Check if a function is available in V2 system.
        
        This can be used to determine whether to use V2 or fallback to V1.
        """
        try:
            from .function_executor import get_v2_executor
            executor = get_v2_executor(self.db)
            available_functions = executor.get_available_functions()
            return function_name in available_functions
        except Exception as e:
            self.logger.warning(f"Failed to check V2 function availability: {str(e)}")
            return False
    
    def get_v2_function_info(self, function_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a V2 function"""
        try:
            from .function_executor import get_v2_executor
            executor = get_v2_executor(self.db)
            return executor.get_function_info(function_name)
        except Exception as e:
            self.logger.warning(f"Failed to get V2 function info: {str(e)}")
            return None

# Global adapter instance
_adapter_instance: Optional[V2FunctionAdapter] = None

def get_v2_adapter(db_session: Session) -> V2FunctionAdapter:
    """Get or create the global V2 function adapter instance"""
    global _adapter_instance
    if _adapter_instance is None:
        _adapter_instance = V2FunctionAdapter(db_session)
    else:
        # Update database session if different
        _adapter_instance.db = db_session
    return _adapter_instance

def execute_nested_function_v2(
    function_name: str,
    db_session: Session,
    input_values: Dict[str, Any],
    **kwargs
) -> Any:
    """
    Convenience function for executing nested functions via V2.
    This is the main entry point for LO execution service.
    """
    adapter = get_v2_adapter(db_session)
    return adapter.execute_nested_function_v2(function_name, input_values, **kwargs)

def execute_system_function_v2(
    function_name: str,
    db_session: Session,
    entity_id: Optional[str] = None,
    attribute_id: Optional[str] = None,
    **kwargs
) -> Any:
    """
    Convenience function for executing system functions via V2.
    This is the main entry point for system function calls.
    """
    adapter = get_v2_adapter(db_session)
    return adapter.execute_system_function_v2(function_name, entity_id, attribute_id, **kwargs)
