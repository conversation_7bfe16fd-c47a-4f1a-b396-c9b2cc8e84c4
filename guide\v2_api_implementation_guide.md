# V2 API Implementation Guide

## Overview

This guide provides a comprehensive implementation plan for creating V2 APIs that support the enhanced schema changes from workflow_temp. The V2 APIs will handle new input types, nested functions, business rules, process intelligence, and enhanced UI generation.

## Core API Changes Required

### 1. Authentication & Authorization APIs

#### Enhanced User Registration
**Endpoint:** `POST /api/v2/auth/register`

**New Features:**
- Support for organizational units
- Role-based registration with inheritance
- Enhanced user metadata
- Audit trail integration

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "first_name": "string",
  "last_name": "string",
  "org_unit_id": "string",
  "roles": ["string"],
  "metadata": {
    "department": "string",
    "team": "string",
    "reports_to": "string"
  }
}
```

**Implementation Changes:**
- Add user_organizations table integration
- Implement role inheritance validation
- Add organizational unit validation
- Enhanced password policies

#### Enhanced Login
**Endpoint:** `POST /api/v2/auth/login`

**New Features:**
- OAuth token management
- Session management with expiration
- Role-based permissions loading
- Organizational context

**Response:**
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user": {
    "user_id": "string",
    "username": "string",
    "roles": ["string"],
    "permissions": ["string"],
    "org_units": ["string"]
  }
}
```

### 2. Global Objectives V2 API

#### Enhanced Global Objectives Fetch
**Endpoint:** `GET /api/v2/global-objectives/`

**New Features:**
- Process mining schema support
- Performance metadata
- Classification filtering
- Book/Chapter organization

**Query Parameters:**
- `tenant_id` (required)
- `classification` (optional)
- `book_id` (optional)
- `include_metadata` (boolean, default: false)
- `include_performance` (boolean, default: false)

**Response:**
```json
{
  "objectives": [
    {
      "go_id": "string",
      "name": "string",
      "description": "string",
      "status": "string",
      "version": "string",
      "classification": "string",
      "primary_entity": "string",
      "tenant_id": "string",
      "tenant_name": "string",
      "book_id": "string",
      "book_name": "string",
      "chapter_id": "string",
      "chapter_name": "string",
      "metadata": {},
      "process_mining_schema": {},
      "performance_metadata": {},
      "created_by": "string",
      "updated_by": "string",
      "created_at": "datetime",
      "updated_at": "datetime"
    }
  ],
  "total": 0,
  "page": 1,
  "page_size": 10
}
```

**Implementation:**
```python
@router.get("/", response_model=GlobalObjectivesResponse)
async def get_global_objectives_v2(
    tenant_id: str = Query(...),
    classification: Optional[str] = Query(None),
    book_id: Optional[str] = Query(None),
    include_metadata: bool = Query(False),
    include_performance: bool = Query(False),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """Enhanced global objectives with filtering and metadata support."""
    
    query = """
    SELECT go_id, name, description, status, version, classification,
           primary_entity, tenant_id, tenant_name, book_id, book_name,
           chapter_id, chapter_name, created_by, updated_by,
           created_at, updated_at
    """
    
    if include_metadata:
        query += ", metadata"
    if include_performance:
        query += ", process_mining_schema, performance_metadata"
    
    query += """
    FROM workflow_runtime.global_objectives
    WHERE tenant_id = :tenant_id AND deleted_mark = false
    """
    
    params = {"tenant_id": tenant_id}
    
    if classification:
        query += " AND classification = :classification"
        params["classification"] = classification
    
    if book_id:
        query += " AND book_id = :book_id"
        params["book_id"] = book_id
    
    # Add pagination
    offset = (page - 1) * page_size
    query += f" ORDER BY created_at DESC LIMIT {page_size} OFFSET {offset}"
    
    result = db.execute(text(query), params).fetchall()
    
    # Get total count
    count_query = "SELECT COUNT(*) FROM workflow_runtime.global_objectives WHERE tenant_id = :tenant_id AND deleted_mark = false"
    if classification:
        count_query += " AND classification = :classification"
    if book_id:
        count_query += " AND book_id = :book_id"
    
    total = db.execute(text(count_query), params).scalar()
    
    return {
        "objectives": [dict(row._mapping) for row in result],
        "total": total,
        "page": page,
        "page_size": page_size
    }
```

### 3. Enhanced Local Objective Input API

#### V2 Input Stack API
**Endpoint:** `GET /api/v2/instances/{instance_id}/inputs`

**New Features:**
- Four input types: user, system, information, system_dependent
- Nested function execution for system inputs
- Dynamic dropdown data sources
- Field dependencies and validations
- UI control specifications

**Query Parameters:**
- `dependent_input_id` (optional) - For resolving specific dependent inputs
- `parent_values` (optional) - Parent field values for dependencies
- `include_validations` (boolean, default: true)
- `include_dropdowns` (boolean, default: true)

**Response:**
```json
{
  "local_objective": "string",
  "input_categories": {
    "user_inputs": [
      {
        "input_id": "string",
        "attribute_id": "string",
        "entity_id": "string",
        "display_name": "string",
        "data_type": "string",
        "ui_control": "string",
        "required": true,
        "is_visible": true,
        "input_value": null,
        "default_value": "string",
        "help_text": "string",
        "validations": [
          {
            "rule": "string",
            "expression": "string",
            "error_message": "string"
          }
        ],
        "allowed_values": ["string"],
        "dropdown_options": [
          {
            "value": "string",
            "label": "string"
          }
        ]
      }
    ],
    "system_inputs": [
      {
        "input_id": "string",
        "attribute_id": "string",
        "display_name": "string",
        "input_value": "auto-calculated",
        "nested_functions": [
          {
            "function_name": "string",
            "parameters": {},
            "output_to": "string"
          }
        ]
      }
    ],
    "information_inputs": [
      {
        "input_id": "string",
        "display_name": "string",
        "input_value": "display-only",
        "read_only": true
      }
    ],
    "dependent_inputs": [
      {
        "input_id": "string",
        "attribute_id": "string",
        "display_name": "string",
        "dependencies": ["string"],
        "needs_parent_value": true,
        "parent_ids": ["string"]
      }
    ]
  },
  "ui_configuration": {
    "layout": "form",
    "sections": [
      {
        "title": "string",
        "fields": ["string"]
      }
    ]
  }
}
```

**Implementation:**
```python
@router.get("/instances/{instance_id}/inputs", response_model=EnhancedInputResponse)
async def get_local_objective_inputs_v2(
    request: Request,
    instance_id: str = Path(...),
    dependent_input_id: Optional[str] = Query(None),
    include_validations: bool = Query(True),
    include_dropdowns: bool = Query(True),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """Enhanced input API with categorized inputs and UI configuration."""
    
    # Extract parent values from query parameters
    parent_values = {}
    for key, value in request.query_params.items():
        if key not in ['instance_id', 'dependent_input_id', 'include_validations', 'include_dropdowns']:
            parent_values[key] = value
    
    # Get workflow instance and validate
    instance, current_lo_id = fetch_workflow_instance_for_inputs(db, instance_id)
    
    # Fetch all input fields with enhanced metadata
    input_fields = fetch_enhanced_input_fields(db, current_lo_id, include_validations)
    
    # Categorize inputs by type
    categorized_inputs = categorize_inputs_v2(input_fields)
    
    # Load dropdown options if requested
    if include_dropdowns:
        categorized_inputs = load_dropdown_options_v2(db, categorized_inputs, parent_values)
    
    # Execute nested functions for system inputs
    categorized_inputs = execute_system_functions_v2(db, current_lo_id, categorized_inputs)
    
    # Resolve dependent inputs
    if dependent_input_id and parent_values:
        categorized_inputs = resolve_dependent_input_v2(
            db, current_lo_id, categorized_inputs, dependent_input_id, parent_values
        )
    
    # Load UI configuration
    ui_config = load_ui_configuration_v2(db, current_lo_id)
    
    return {
        "local_objective": current_lo_id,
        "input_categories": categorized_inputs,
        "ui_configuration": ui_config
    }
```

### 4. Enhanced Execution API

#### V2 Execution API
**Endpoint:** `POST /api/v2/instances/{instance_id}/execute`

**New Features:**
- Enhanced nested function support
- Business rule validation
- Advanced data mapping
- Performance tracking
- Rollback capabilities

**Request Body:**
```json
{
  "input_data": {
    "attribute_id": "value"
  },
  "user_id": "string",
  "execution_options": {
    "validate_business_rules": true,
    "track_performance": true,
    "enable_rollback": false,
    "dry_run": false
  }
}
```

**Response:**
```json
{
  "status": "Completed",
  "message": "string",
  "execution_id": "string",
  "output": {
    "attribute_id": {
      "value": "result",
      "display_name": "string"
    }
  },
  "next_lo_id": "string",
  "performance_metrics": {
    "execution_time_ms": 1500,
    "function_calls": 5,
    "validation_time_ms": 200
  },
  "business_rule_results": [
    {
      "rule_id": "string",
      "rule_name": "string",
      "status": "passed",
      "message": "string"
    }
  ],
  "warnings": ["string"],
  "rollback_point": "string"
}
```

### 5. Business Rules Management API

#### Business Rules CRUD
**Endpoints:**
- `GET /api/v2/business-rules/` - List business rules
- `POST /api/v2/business-rules/` - Create business rule
- `GET /api/v2/business-rules/{rule_id}` - Get business rule
- `PUT /api/v2/business-rules/{rule_id}` - Update business rule
- `DELETE /api/v2/business-rules/{rule_id}` - Delete business rule

**Business Rule Structure:**
```json
{
  "business_rules_id": "string",
  "go_id": "string",
  "rule_name": "string",
  "rule_description": "string",
  "rule_inputs": "string",
  "rule_operation": "string",
  "rule_outputs": "string",
  "rule_success_message": "string",
  "rule_error_message": "string",
  "rule_validation_type": "string",
  "conditions": [
    {
      "field": "string",
      "operator": "string",
      "value": "string"
    }
  ]
}
```

### 6. Process Intelligence API

#### Performance Analytics
**Endpoint:** `GET /api/v2/analytics/performance/{go_id}`

**Response:**
```json
{
  "go_id": "string",
  "performance_metadata": {
    "cycle_time": "string",
    "number_of_pathways": 5,
    "sla_thresholds": [
      {
        "threshold_name": "string",
        "threshold_value": "string"
      }
    ],
    "volume_metrics": {
      "average_volume": 100,
      "peak_volume": 500,
      "unit": "requests/day"
    }
  },
  "bottleneck_analysis": [
    {
      "lo_name": "string",
      "average_wait_time": "string",
      "queue_length": 10,
      "resource_utilization": 85
    }
  ],
  "pathway_frequency": [
    {
      "pathway_name": "string",
      "frequency": 75,
      "percentage": 60,
      "success_rate": 95
    }
  ]
}
```

### 7. UI Configuration API

#### Dynamic UI Generation
**Endpoint:** `GET /api/v2/ui-config/{lo_id}`

**Response:**
```json
{
  "lo_id": "string",
  "ui_stacks": [
    {
      "stack_id": "string",
      "ui_type": "form",
      "description": "string",
      "ui_items": [
        {
          "item_id": "string",
          "control_type": "input",
          "label": "string",
          "placeholder": "string",
          "validation_rule": "string",
          "sequence_number": 1
        }
      ]
    }
  ],
  "entity_attribute_stack": [
    {
      "entity_id": "string",
      "attribute_id": "string",
      "ui_form": "INPUT_TEXT",
      "style_parameters": {},
      "helper_tip": "string",
      "read_only": false
    }
  ]
}
```

## Implementation Strategy

### Phase 1: Core API Migration (Weeks 1-2)
1. **Update Authentication APIs**
   - Enhance registration with organizational units
   - Add OAuth token management
   - Implement role inheritance

2. **Migrate Global Objectives API**
   - Add new metadata fields
   - Implement filtering and pagination
   - Add performance metadata support

3. **Enhance Input Stack API**
   - Implement four input types
   - Add nested function execution
   - Support dynamic dropdowns

### Phase 2: Enhanced Features (Weeks 3-4)
1. **Upgrade Execution API**
   - Add business rule validation
   - Implement performance tracking
   - Add rollback capabilities

2. **Business Rules Management**
   - Create CRUD operations
   - Implement rule engine
   - Add validation framework

### Phase 3: Advanced Features (Weeks 5-6)
1. **Process Intelligence APIs**
   - Performance analytics
   - Bottleneck analysis
   - Predictive insights

2. **UI Configuration APIs**
   - Dynamic UI generation
   - Layout management
   - Style configuration

### Phase 4: Testing & Optimization (Weeks 7-8)
1. **Comprehensive Testing**
   - Unit tests for all endpoints
   - Integration tests
   - Performance testing

2. **Documentation & Migration**
   - API documentation
   - Migration guides
   - Backward compatibility

## Database Migration Scripts

### Core Table Migrations
```sql
-- Add new columns to global_objectives
ALTER TABLE workflow_runtime.global_objectives 
ADD COLUMN metadata jsonb,
ADD COLUMN auto_id SERIAL,
ADD COLUMN primary_entity varchar(255),
ADD COLUMN classification varchar(255),
ADD COLUMN tenant_name varchar(255),
ADD COLUMN book_id varchar(255),
ADD COLUMN book_name varchar(255),
ADD COLUMN chapter_id varchar(255),
ADD COLUMN chapter_name varchar(255),
ADD COLUMN created_by varchar(255),
ADD COLUMN updated_by varchar(255);

-- Add new columns to local_objectives
ALTER TABLE workflow_runtime.local_objectives
ADD COLUMN auto_id SERIAL,
ADD COLUMN agent_type varchar(50),
ADD COLUMN execution_rights varchar(255),
ADD COLUMN status varchar(50) DEFAULT 'Active',
ADD COLUMN version varchar(50) DEFAULT '1.0',
ADD COLUMN description varchar(255),
ADD COLUMN created_by varchar(255),
ADD COLUMN updated_by varchar(255);

-- Add new columns to entities
ALTER TABLE workflow_runtime.entities
ADD COLUMN display_name varchar(255),
ADD COLUMN created_by varchar(255),
ADD COLUMN updated_by varchar(255);

-- Add new columns to entity_attributes
ALTER TABLE workflow_runtime.entity_attributes
ADD COLUMN display_name varchar(255),
ADD COLUMN is_primary_key boolean DEFAULT false,
ADD COLUMN is_foreign_key boolean DEFAULT false,
ADD COLUMN is_unique boolean DEFAULT false,
ADD COLUMN min_value varchar(50),
ADD COLUMN max_value varchar(50),
ADD COLUMN min_length integer,
ADD COLUMN max_length integer,
ADD COLUMN pattern varchar(255),
ADD COLUMN is_calculated boolean DEFAULT false,
ADD COLUMN calculation_formula text,
ADD COLUMN created_by varchar(255),
ADD COLUMN updated_by varchar(255);
```

### New Table Creations
```sql
-- Create business_rules table
CREATE TABLE workflow_runtime.business_rules (
    id SERIAL PRIMARY KEY,
    business_rules_id varchar NOT NULL,
    go_id varchar NOT NULL,
    rule_name varchar NOT NULL,
    rule_description text,
    rule_inputs text,
    rule_operation text,
    rule_outputs text,
    rule_success_message text,
    rule_error_message text,
    rule_output_attribute_id varchar,
    rule_output_attribute_name varchar,
    rule_validation_type varchar,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by varchar(255),
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(255),
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id)
);

-- Create business_rule_conditions table
CREATE TABLE workflow_runtime.business_rule_conditions (
    id SERIAL PRIMARY KEY,
    rules_id varchar NOT NULL,
    business_rules_id varchar NOT NULL,
    go_id varchar NOT NULL,
    field varchar NOT NULL,
    operator varchar NOT NULL,
    value text,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by varchar(255),
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(255),
    FOREIGN KEY (business_rules_id) REFERENCES workflow_runtime.business_rules(business_rules_id)
);
```

## Error Handling & Validation

### Enhanced Error Responses
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Business rule validation failed",
    "details": {
      "rule_id": "BR001",
      "rule_name": "Leave Balance Check",
      "failed_condition": "insufficient_balance",
      "current_value": 5,
      "required_value": 10
    },
    "timestamp": "2025-01-01T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

### Validation Framework
```python
class BusinessRuleValidator:
    def __init__(self, db: Session):
        self.db = db
    
    def validate_rules(self, go_id: str, input_data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate all business rules for a global objective."""
        rules = self.get_business_rules(go_id)
        results = []
        
        for rule in rules:
            result = self.validate_single_rule(rule, input_data)
            results.append(result)
        
        return results
    
    def validate_single_rule(self, rule: BusinessRule, input_data: Dict[str, Any]) -> ValidationResult:
        """Validate a single business rule."""
        conditions = self.get_rule_conditions(rule.business_rules_id)
        
        for condition in conditions:
            if not self.evaluate_condition(condition, input_data):
                return ValidationResult(
                    rule_id=rule.business_rules_id,
                    status="failed",
                    message=rule.rule_error_message,
                    failed_condition=condition.field
                )
        
        return ValidationResult(
            rule_id=rule.business_rules_id,
            status="passed",
            message=rule.rule_success_message
        )
```

## Testing Strategy

### Unit Tests
```python
class TestV2APIs:
    def test_enhanced_global_objectives(self):
        """Test enhanced global objectives API with metadata."""
        response = client.get(
            "/api/v2/global-objectives/",
            params={
                "tenant_id": "t001",
                "include_metadata": True,
                "classification": "core"
            }
        )
        assert response.status_code == 200
        assert "metadata" in response.json()["objectives"][0]
    
    def test_categorized_inputs(self):
        """Test categorized input API."""
        response = client.get(f"/api/v2/instances/{instance_id}/inputs")
        data = response.json()
        
        assert "input_categories" in data
        assert "user_inputs" in data["input_categories"]
        assert "system_inputs" in data["input_categories"]
        assert "information_inputs" in data["input_categories"]
        assert "dependent_inputs" in data["input_categories"]
    
    def test_business_rule_validation(self):
        """Test business rule validation during execution."""
        response = client.post(
            f"/api/v2/instances/{instance_id}/execute",
            json={
                "input_data": {"leave_days": 15},
                "user_id": "user123",
                "execution_options": {"validate_business_rules": True}
            }
        )
        data = response.json()
        assert "business_rule_results" in data
```

### Integration Tests
```python
class TestV2Integration:
    def test_end_to_end_workflow(self):
        """Test complete workflow execution with V2 APIs."""
        # Create instance
        instance = self.create_workflow_instance()
        
        # Get inputs with enhanced features
        inputs = self.get_enhanced_inputs(instance.instance_id)
        
        # Execute with business rules
        result = self.execute_with_business_rules(instance.instance_id, inputs)
        
        # Verify performance tracking
        assert "performance_metrics" in result
        assert result["performance_metrics"]["execution_time_ms"] > 0
```

## Backward Compatibility

### V1 API Deprecation Strategy
1. **Maintain V1 endpoints** for 6 months
2. **Add deprecation headers** to V1 responses
3. **Provide migration guides** for each endpoint
4. **Gradual feature removal** from V1

### Migration Path
```python
# V1 to V2 adapter
class V1ToV2Adapter:
    def adapt_global_objectives_response(self, v2_response):
        """Convert V2 response to V1 format for backward compatibility."""
        return {
            "objective_id": v2_response["go_id"],
            "contextual_id": v2_response["go_id"],  # Legacy field
            "name": v2_response["name"],
            "tenant_id": v2_response["tenant_id"],
            "version": v2_response["version"],
            "status": v2_response["status"],
            "deleted_mark": False
        }
```

This comprehensive implementation guide provides the foundation for creating robust V2 APIs that support all the enhanced features from the workflow_temp schema while maintaining backward compatibility and ensuring smooth migration.
