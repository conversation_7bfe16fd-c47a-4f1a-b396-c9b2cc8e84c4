-- Database Migration Script: workflow_temp to workflow_runtime
-- This script migrates schema changes and data from workflow_temp to workflow_runtime

-- ============================================================================
-- PHASE 1: CORE TABLE MODIFICATIONS
-- ============================================================================

-- 1. Global Objectives Enhancements
ALTER TABLE workflow_runtime.global_objectives 
ADD COLUMN IF NOT EXISTS metadata jsonb,
ADD COLUMN IF NOT EXISTS auto_id SERIAL,
ADD COLUMN IF NOT EXISTS primary_entity varchar(255),
ADD COLUMN IF NOT EXISTS classification varchar(255),
ADD COLUMN IF NOT EXISTS tenant_name varchar(255),
ADD COLUMN IF NOT EXISTS book_id varchar(255),
ADD COLUMN IF NOT EXISTS book_name varchar(255),
ADD COLUMN IF NOT EXISTS chapter_id varchar(255),
ADD COLUMN IF NOT EXISTS chapter_name varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 2. Local Objectives Enhancements
ALTER TABLE workflow_runtime.local_objectives
ADD COLUMN IF NOT EXISTS auto_id SERIAL,
ADD COLUMN IF NOT EXISTS agent_type varchar(50),
ADD COLUMN IF NOT EXISTS execution_rights varchar(255),
ADD COLUMN IF NOT EXISTS status varchar(50) DEFAULT 'Active',
ADD COLUMN IF NOT EXISTS version varchar(50) DEFAULT '1.0',
ADD COLUMN IF NOT EXISTS description varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 3. Entities Enhancements
ALTER TABLE workflow_runtime.entities
ADD COLUMN IF NOT EXISTS display_name varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 4. Entity Attributes Enhancements
ALTER TABLE workflow_runtime.entity_attributes
ADD COLUMN IF NOT EXISTS display_name varchar(255),
ADD COLUMN IF NOT EXISTS is_primary_key boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS is_foreign_key boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS is_unique boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS min_value varchar(50),
ADD COLUMN IF NOT EXISTS max_value varchar(50),
ADD COLUMN IF NOT EXISTS min_length integer,
ADD COLUMN IF NOT EXISTS max_length integer,
ADD COLUMN IF NOT EXISTS pattern varchar(255),
ADD COLUMN IF NOT EXISTS is_calculated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS calculation_formula text,
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 5. Entity Business Rules Modifications
ALTER TABLE workflow_runtime.entity_business_rules
ALTER COLUMN inputs TYPE jsonb USING inputs::jsonb,
ALTER COLUMN outputs TYPE jsonb USING outputs::jsonb,
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 6. Entity Relationships Enhancements
ALTER TABLE workflow_runtime.entity_relationships
ADD COLUMN IF NOT EXISTS on_delete varchar(50),
ADD COLUMN IF NOT EXISTS on_update varchar(50),
ADD COLUMN IF NOT EXISTS foreign_key_type varchar(50),
ADD COLUMN IF NOT EXISTS description text,
ADD COLUMN IF NOT EXISTS version varchar(50),
ADD COLUMN IF NOT EXISTS status varchar(50),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 7. LO Input Items Enhancements
ALTER TABLE workflow_runtime.lo_input_items
ADD COLUMN IF NOT EXISTS read_only boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS agent_type varchar(50),
ADD COLUMN IF NOT EXISTS dependent_attribute boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS dependent_attribute_value text,
ADD COLUMN IF NOT EXISTS enum_values jsonb,
ADD COLUMN IF NOT EXISTS default_value text,
ADD COLUMN IF NOT EXISTS information_field boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS constant_field boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS entity_id varchar(255),
ADD COLUMN IF NOT EXISTS attribute_id varchar(255),
ADD COLUMN IF NOT EXISTS entity_name varchar(255),
ADD COLUMN IF NOT EXISTS attribute_name varchar(255);

-- 8. LO Input Stack Enhancements
ALTER TABLE workflow_runtime.lo_input_stack
ADD COLUMN IF NOT EXISTS lo_input_stack_id varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 9. LO Output Items Enhancements
ALTER TABLE workflow_runtime.lo_output_items
ADD COLUMN IF NOT EXISTS nested_function_id varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 10. LO Output Stack Enhancements
ALTER TABLE workflow_runtime.lo_output_stack
ADD COLUMN IF NOT EXISTS lo_output_stack_id varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 11. LO Data Mapping Stack Enhancements
ALTER TABLE workflow_runtime.lo_data_mapping_stack
ADD COLUMN IF NOT EXISTS lo_data_mapping_stack_id varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 12. LO Data Mappings Enhancements
ALTER TABLE workflow_runtime.lo_data_mappings
ADD COLUMN IF NOT EXISTS auto_id SERIAL,
ADD COLUMN IF NOT EXISTS source_entity varchar(255),
ADD COLUMN IF NOT EXISTS source_attribute varchar(255),
ADD COLUMN IF NOT EXISTS target_entity varchar(255),
ADD COLUMN IF NOT EXISTS target_attribute varchar(255),
ADD COLUMN IF NOT EXISTS lo_data_mapping_item_id varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 13. LO Nested Functions Enhancements
ALTER TABLE workflow_runtime.lo_nested_functions
ADD COLUMN IF NOT EXISTS description varchar(255),
ADD COLUMN IF NOT EXISTS returns varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 14. Execution Pathways Modifications
-- Note: This requires careful handling due to ID type change
ALTER TABLE workflow_runtime.execution_pathways
ADD COLUMN IF NOT EXISTS execution_pathway_id varchar(255),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 15. Execution Pathway Conditions Enhancements
ALTER TABLE workflow_runtime.execution_pathway_conditions
ADD COLUMN IF NOT EXISTS execution_pathway_condition_id varchar(255),
ADD COLUMN IF NOT EXISTS multiple_conditions varchar(255),
ADD COLUMN IF NOT EXISTS multiple_conditions_operator varchar(50),
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 16. Terminal Pathways Modifications
ALTER TABLE workflow_runtime.terminal_pathways
ADD COLUMN IF NOT EXISTS go_id varchar(255),
ADD COLUMN IF NOT EXISTS pathway_data jsonb,
ADD COLUMN IF NOT EXISTS sequence_number integer;

-- 17. UI Elements Enhancements
ALTER TABLE workflow_runtime.ui_elements
ADD COLUMN IF NOT EXISTS entity_id varchar(255),
ADD COLUMN IF NOT EXISTS attribute_id varchar(255),
ADD COLUMN IF NOT EXISTS read_only boolean DEFAULT false;

-- 18. Data Mappings Enhancements
ALTER TABLE workflow_runtime.data_mappings
ADD COLUMN IF NOT EXISTS auto_id SERIAL,
ADD COLUMN IF NOT EXISTS metadata jsonb;

-- 19. Output Items Enhancements
ALTER TABLE workflow_runtime.output_items
ADD COLUMN IF NOT EXISTS auto_id SERIAL,
ADD COLUMN IF NOT EXISTS metadata jsonb;

-- 20. Output Stack Modifications
ALTER TABLE workflow_runtime.output_stack
ALTER COLUMN go_id DROP NOT NULL,
ADD COLUMN IF NOT EXISTS created_by varchar(255),
ADD COLUMN IF NOT EXISTS updated_by varchar(255);

-- 21. Roles Enhancements
ALTER TABLE workflow_runtime.roles
ADD COLUMN IF NOT EXISTS id SERIAL;

-- 22. Users Enhancements
ALTER TABLE workflow_runtime.users
ADD COLUMN IF NOT EXISTS id SERIAL;

-- 23. User Roles Enhancements
ALTER TABLE workflow_runtime.user_roles
ADD COLUMN IF NOT EXISTS id SERIAL;

-- 24. Role Inheritance Enhancements
ALTER TABLE workflow_runtime.role_inheritance
ADD COLUMN IF NOT EXISTS id SERIAL;

-- 25. Attribute Validations Enhancements
ALTER TABLE workflow_runtime.attribute_validations
ADD COLUMN IF NOT EXISTS validation_type varchar(50),
ADD COLUMN IF NOT EXISTS validation_rule text,
ADD COLUMN IF NOT EXISTS error_message text,
ADD COLUMN IF NOT EXISTS validation_timing varchar(50),
ADD COLUMN IF NOT EXISTS validation_id integer,
ADD COLUMN IF NOT EXISTS id SERIAL,
ADD COLUMN IF NOT EXISTS executable_rule text;

-- ============================================================================
-- PHASE 2: NEW TABLE CREATIONS
-- ============================================================================

-- 1. Business Rules
CREATE TABLE IF NOT EXISTS workflow_runtime.business_rules (
    id SERIAL PRIMARY KEY,
    business_rules_id varchar NOT NULL UNIQUE,
    go_id varchar NOT NULL,
    rule_name varchar NOT NULL,
    rule_description text,
    rule_inputs text,
    rule_operation text,
    rule_outputs text,
    rule_success_message text,
    rule_error_message text,
    rule_output_attribute_id varchar,
    rule_output_attribute_name varchar,
    rule_validation_type varchar,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by varchar(255),
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(255),
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id)
);

-- 2. Business Rule Conditions
CREATE TABLE IF NOT EXISTS workflow_runtime.business_rule_conditions (
    id SERIAL PRIMARY KEY,
    rules_id varchar NOT NULL,
    business_rules_id varchar NOT NULL,
    go_id varchar NOT NULL,
    field varchar NOT NULL,
    operator varchar NOT NULL,
    value text,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by varchar(255),
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(255),
    FOREIGN KEY (business_rules_id) REFERENCES workflow_runtime.business_rules(business_rules_id)
);

-- 3. LO UI Stacks
CREATE TABLE IF NOT EXISTS workflow_runtime.lo_ui_stacks (
    id SERIAL PRIMARY KEY,
    lo_ui_stack_id varchar NOT NULL UNIQUE,
    lo_id varchar NOT NULL,
    ui_type varchar(50),
    description text,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by varchar(255),
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(255),
    FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id)
);

-- 4. LO UI Items
CREATE TABLE IF NOT EXISTS workflow_runtime.lo_ui_items (
    id SERIAL PRIMARY KEY,
    lo_ui_item_id varchar NOT NULL UNIQUE,
    lo_ui_stack_id varchar NOT NULL,
    control_type varchar(50),
    label varchar(255),
    placeholder varchar(255),
    validation_rule text,
    sequence_number integer,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by varchar(255),
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(255),
    FOREIGN KEY (lo_ui_stack_id) REFERENCES workflow_runtime.lo_ui_stacks(lo_ui_stack_id)
);

-- 5. LO UI Entity Attribute Stack
CREATE TABLE IF NOT EXISTS workflow_runtime.lo_ui_entity_attribute_stack (
    id SERIAL PRIMARY KEY,
    lo_ui_stack_id varchar NOT NULL,
    entity_id varchar NOT NULL,
    attribute_id varchar NOT NULL,
    ui_form varchar(50),
    style_parameters jsonb,
    helper_tip text,
    read_only boolean DEFAULT false,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by varchar(255),
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(255),
    FOREIGN KEY (lo_ui_stack_id) REFERENCES workflow_runtime.lo_ui_stacks(lo_ui_stack_id),
    FOREIGN KEY (entity_id) REFERENCES workflow_runtime.entities(entity_id),
    FOREIGN KEY (attribute_id) REFERENCES workflow_runtime.entity_attributes(attribute_id)
);

-- 6. Performance Metadata
CREATE TABLE IF NOT EXISTS workflow_runtime.performance_metadata (
    id SERIAL PRIMARY KEY,
    go_id varchar NOT NULL,
    cycle_time varchar(50),
    number_of_pathways integer,
    volume_metrics jsonb,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id)
);

-- 7. SLA Thresholds
CREATE TABLE IF NOT EXISTS workflow_runtime.sla_thresholds (
    id SERIAL PRIMARY KEY,
    go_id varchar NOT NULL,
    threshold_name varchar(255),
    threshold_value varchar(255),
    threshold_unit varchar(50),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id)
);

-- 8. Volume Metrics
CREATE TABLE IF NOT EXISTS workflow_runtime.volume_metrics (
    id SERIAL PRIMARY KEY,
    go_id varchar NOT NULL,
    average_volume integer,
    peak_volume integer,
    unit varchar(50),
    measurement_period varchar(50),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id)
);

-- 9. Bottleneck Analysis
CREATE TABLE IF NOT EXISTS workflow_runtime.bottleneck_analysis (
    id SERIAL PRIMARY KEY,
    go_id varchar NOT NULL,
    lo_id varchar NOT NULL,
    lo_name varchar(255),
    average_wait_time varchar(50),
    queue_length integer,
    resource_utilization integer,
    analysis_date timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id),
    FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id)
);

-- 10. Pathway Frequency
CREATE TABLE IF NOT EXISTS workflow_runtime.pathway_frequency (
    id SERIAL PRIMARY KEY,
    go_id varchar NOT NULL,
    pathway_name varchar(255),
    frequency integer,
    percentage decimal(5,2),
    success_rate decimal(5,2),
    analysis_period varchar(50),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id)
);

-- 11. Advanced Process Intelligence
CREATE TABLE IF NOT EXISTS workflow_runtime.advanced_process_intelligence (
    id SERIAL PRIMARY KEY,
    go_id varchar NOT NULL,
    intelligence_type varchar(50),
    intelligence_data jsonb,
    insights text,
    recommendations text,
    confidence_score decimal(3,2),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id)
);

-- 12. Dropdown Data Sources
CREATE TABLE IF NOT EXISTS workflow_runtime.dropdown_data_sources (
    id SERIAL PRIMARY KEY,
    input_item_id integer NOT NULL,
    input_stack_id varchar,
    source_type varchar(50) NOT NULL, -- 'database', 'function', 'static'
    query_text text,
    function_name varchar(255),
    function_params jsonb,
    value_field varchar(255),
    display_field varchar(255),
    depends_on_fields jsonb,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (input_item_id) REFERENCES workflow_runtime.lo_input_items(id)
);

-- ============================================================================
-- PHASE 3: DATA MIGRATION FROM workflow_temp (if exists)
-- ============================================================================

-- Note: These statements will only execute if workflow_temp schema exists
-- and contains data that needs to be migrated

DO $$
BEGIN
    -- Check if workflow_temp schema exists
    IF EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'workflow_temp') THEN
        
        -- Migrate Global Objectives metadata
        UPDATE workflow_runtime.global_objectives 
        SET 
            metadata = wt.metadata,
            primary_entity = wt.primary_entity,
            classification = wt.classification,
            tenant_name = wt.tenant_name,
            book_id = wt.book_id,
            book_name = wt.book_name,
            chapter_id = wt.chapter_id,
            chapter_name = wt.chapter_name,
            created_by = wt.created_by,
            updated_by = wt.updated_by
        FROM workflow_temp.global_objectives wt
        WHERE workflow_runtime.global_objectives.go_id = wt.go_id;
        
        -- Migrate Local Objectives enhancements
        UPDATE workflow_runtime.local_objectives 
        SET 
            agent_type = wt.agent_type,
            execution_rights = wt.execution_rights,
            status = wt.status,
            version = wt.version,
            description = wt.description,
            created_by = wt.created_by,
            updated_by = wt.updated_by
        FROM workflow_temp.local_objectives wt
        WHERE workflow_runtime.local_objectives.lo_id = wt.lo_id;
        
        -- Migrate Entity display names
        UPDATE workflow_runtime.entities 
        SET 
            display_name = wt.display_name,
            created_by = wt.created_by,
            updated_by = wt.updated_by
        FROM workflow_temp.entities wt
        WHERE workflow_runtime.entities.entity_id = wt.entity_id;
        
        -- Migrate Entity Attributes enhancements
        UPDATE workflow_runtime.entity_attributes 
        SET 
            display_name = wt.display_name,
            is_primary_key = wt.is_primary_key,
            is_foreign_key = wt.is_foreign_key,
            is_unique = wt.is_unique,
            min_value = wt.min_value,
            max_value = wt.max_value,
            min_length = wt.min_length,
            max_length = wt.max_length,
            pattern = wt.pattern,
            is_calculated = wt.is_calculated,
            calculation_formula = wt.calculation_formula,
            created_by = wt.created_by,
            updated_by = wt.updated_by
        FROM workflow_temp.entity_attributes wt
        WHERE workflow_runtime.entity_attributes.attribute_id = wt.attribute_id;
        
        -- Migrate Business Rules (if they exist in workflow_temp)
        INSERT INTO workflow_runtime.business_rules (
            business_rules_id, go_id, rule_name, rule_description,
            rule_inputs, rule_operation, rule_outputs,
            rule_success_message, rule_error_message,
            rule_output_attribute_id, rule_output_attribute_name,
            rule_validation_type, created_by, updated_by
        )
        SELECT 
            business_rules_id, go_id, rule_name, rule_description,
            rule_inputs, rule_operation, rule_outputs,
            rule_success_message, rule_error_message,
            rule_output_attribute_id, rule_output_attribute_name,
            rule_validation_type, created_by, updated_by
        FROM workflow_temp.business_rules
        ON CONFLICT (business_rules_id) DO NOTHING;
        
        RAISE NOTICE 'Data migration from workflow_temp completed successfully';
    ELSE
        RAISE NOTICE 'workflow_temp schema not found, skipping data migration';
    END IF;
END $$;

-- ============================================================================
-- PHASE 4: INDEX CREATION FOR PERFORMANCE
-- ============================================================================

-- Indexes for new columns
CREATE INDEX IF NOT EXISTS idx_global_objectives_classification ON workflow_runtime.global_objectives(classification);
CREATE INDEX IF NOT EXISTS idx_global_objectives_book_id ON workflow_runtime.global_objectives(book_id);
CREATE INDEX IF NOT EXISTS idx_global_objectives_primary_entity ON workflow_runtime.global_objectives(primary_entity);

CREATE INDEX IF NOT EXISTS idx_local_objectives_agent_type ON workflow_runtime.local_objectives(agent_type);
CREATE INDEX IF NOT EXISTS idx_local_objectives_status ON workflow_runtime.local_objectives(status);

CREATE INDEX IF NOT EXISTS idx_entity_attributes_display_name ON workflow_runtime.entity_attributes(display_name);
CREATE INDEX IF NOT EXISTS idx_entity_attributes_is_primary_key ON workflow_runtime.entity_attributes(is_primary_key);

CREATE INDEX IF NOT EXISTS idx_lo_input_items_entity_id ON workflow_runtime.lo_input_items(entity_id);
CREATE INDEX IF NOT EXISTS idx_lo_input_items_attribute_id ON workflow_runtime.lo_input_items(attribute_id);
CREATE INDEX IF NOT EXISTS idx_lo_input_items_source_type ON workflow_runtime.lo_input_items(source_type);

CREATE INDEX IF NOT EXISTS idx_business_rules_go_id ON workflow_runtime.business_rules(go_id);
CREATE INDEX IF NOT EXISTS idx_business_rule_conditions_business_rules_id ON workflow_runtime.business_rule_conditions(business_rules_id);

CREATE INDEX IF NOT EXISTS idx_dropdown_data_sources_input_item_id ON workflow_runtime.dropdown_data_sources(input_item_id);

-- ============================================================================
-- PHASE 5: UPDATE CONSTRAINTS AND FOREIGN KEYS
-- ============================================================================

-- Add foreign key constraints for new tables
ALTER TABLE workflow_runtime.lo_ui_items 
ADD CONSTRAINT fk_lo_ui_items_stack 
FOREIGN KEY (lo_ui_stack_id) REFERENCES workflow_runtime.lo_ui_stacks(lo_ui_stack_id);

ALTER TABLE workflow_runtime.business_rule_conditions 
ADD CONSTRAINT fk_business_rule_conditions_rules 
FOREIGN KEY (business_rules_id) REFERENCES workflow_runtime.business_rules(business_rules_id);

-- ============================================================================
-- PHASE 6: UPDATE SEQUENCES AND DEFAULT VALUES
-- ============================================================================

-- Update sequences for auto_id columns
SELECT setval('workflow_runtime.global_objectives_auto_id_seq', 
    COALESCE((SELECT MAX(auto_id) FROM workflow_runtime.global_objectives), 1));

SELECT setval('workflow_runtime.local_objectives_auto_id_seq', 
    COALESCE((SELECT MAX(auto_id) FROM workflow_runtime.local_objectives), 1));

-- ============================================================================
-- PHASE 7: DATA VALIDATION AND CLEANUP
-- ============================================================================

-- Validate data integrity
DO $$
DECLARE
    validation_errors INTEGER := 0;
BEGIN
    -- Check for orphaned records
    SELECT COUNT(*) INTO validation_errors
    FROM workflow_runtime.business_rule_conditions brc
    LEFT JOIN workflow_runtime.business_rules br ON brc.business_rules_id = br.business_rules_id
    WHERE br.business_rules_id IS NULL;
    
    IF validation_errors > 0 THEN
        RAISE WARNING 'Found % orphaned business rule conditions', validation_errors;
    END IF;
    
    -- Check for missing display names
    SELECT COUNT(*) INTO validation_errors
    FROM workflow_runtime.entity_attributes
    WHERE display_name IS NULL OR display_name = '';
    
    IF validation_errors > 0 THEN
        RAISE WARNING 'Found % entity attributes without display names', validation_errors;
        
        -- Auto-generate display names from attribute names
        UPDATE workflow_runtime.entity_attributes 
        SET display_name = INITCAP(REPLACE(name, '_', ' '))
        WHERE display_name IS NULL OR display_name = '';
    END IF;
    
    RAISE NOTICE 'Data validation completed';
END $$;

-- ============================================================================
-- MIGRATION COMPLETION LOG
-- ============================================================================

-- Create migration log table if it doesn't exist
CREATE TABLE IF NOT EXISTS workflow_runtime.migration_log (
    id SERIAL PRIMARY KEY,
    migration_name varchar(255) NOT NULL,
    migration_version varchar(50) NOT NULL,
    executed_at timestamp DEFAULT CURRENT_TIMESTAMP,
    executed_by varchar(255) DEFAULT current_user,
    status varchar(50) DEFAULT 'SUCCESS',
    notes text
);

-- Log this migration
INSERT INTO workflow_runtime.migration_log (
    migration_name, 
    migration_version, 
    notes
) VALUES (
    'workflow_temp_to_runtime_migration',
    'v2.0.0',
    'Complete schema migration from workflow_temp to workflow_runtime with enhanced features'
);

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '=================================================================';
    RAISE NOTICE 'MIGRATION COMPLETED SUCCESSFULLY';
    RAISE NOTICE 'Schema: workflow_runtime has been enhanced with workflow_temp features';
    RAISE NOTICE 'New tables created: business_rules, lo_ui_stacks, performance_metadata, etc.';
    RAISE NOTICE 'Enhanced tables: global_objectives, local_objectives, entity_attributes, etc.';
    RAISE NOTICE 'Next steps: Update application APIs to use new schema features';
    RAISE NOTICE '=================================================================';
END $$;
