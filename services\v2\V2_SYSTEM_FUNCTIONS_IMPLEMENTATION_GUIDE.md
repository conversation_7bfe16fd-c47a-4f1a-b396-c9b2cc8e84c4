# V2 System Functions Implementation Guide

## Overview

The V2 System Functions architecture provides a **uniform calling mechanism** for all system functions used by Local Objectives (LOs) and nested functions. This replaces the problematic V1 system with a microservices-based approach that eliminates parameter mismatches and provides better debugging capabilities.

## Key Features

### ✅ **Uniform Calling Mechanism**
- **Single Input Format**: All functions receive the same standardized `SystemFunctionInput` model
- **Consistent Parameters**: Functions extract what they need from a common input structure
- **No More Type Mismatches**: Eliminates `fetch_records() got an unexpected keyword argument` errors

### ✅ **Microservices Architecture**
- **Separate Function Files**: Each function in its own file for better maintainability
- **Centralized Routing**: Function executor routes to appropriate functions
- **Enhanced Logging**: Detailed logging with execution context and timing

### ✅ **Backward Compatibility**
- **V2 Adapter**: Seamless integration with existing LO execution service
- **Legacy Support**: Supports both V1 and V2 calling patterns
- **Gradual Migration**: Can migrate functions one by one

## Architecture Components

### 1. **Core Models** (`models.py`)
```python
class SystemFunctionInput(BaseModel):
    function_name: str
    go_id: Optional[str] = None
    lo_id: Optional[str] = None
    primary_entity_id: Optional[str] = None
    primary_attribute_id: Optional[str] = None
    input_values: Dict[str, Any] = {}
    # ... additional context fields
```

### 2. **Base Function Class** (`base_function.py`)
```python
class BaseSystemFunction(ABC):
    @abstractmethod
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Any:
        pass
    
    def execute(self, input_data: SystemFunctionInput) -> SystemFunctionOutput:
        # Standardized execution with logging, timing, error handling
```

### 3. **Function Executor** (`function_executor.py`)
```python
class V2FunctionExecutor:
    def execute_function(self, function_name: str, input_values: Dict[str, Any], **kwargs) -> SystemFunctionOutput:
        # Routes to appropriate function and executes with standardized input
```

### 4. **V2 Adapter** (`v2_adapter.py`)
```python
def execute_nested_function_v2(function_name: str, db_session: Session, input_values: Dict[str, Any], **kwargs) -> Any:
    # Bridge between LO execution service and V2 functions
```

## Function Implementation Pattern

### Example: Fetch Records Function
```python
class FetchRecordsFunction(BaseSystemFunction):
    def get_required_inputs(self) -> list[str]:
        return ["table"]  # Only table is required
    
    def get_optional_inputs(self) -> list[str]:
        return ["filters", "columns", "limit", "order_by", ...]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> List[Dict[str, Any]]:
        # Get table name
        table = self.get_input_value(input_data, "table")
        
        # Process any input as potential filter
        for key, val in input_data.input_values.items():
            if key not in reserved_params and val is not None:
                # Add as filter condition
                
        # Execute query and return results
```

## Integration with LO Execution Service

### Current Integration Points

1. **Nested Function Execution** (in `_execute_nested_function`):
```python
# OLD V1 WAY (problematic):
result_value = function_repository.auto_execute(function_name, self.db, **execution_params)

# NEW V2 WAY (recommended):
from app.services.v2.v2_adapter import execute_nested_function_v2
result_value = execute_nested_function_v2(
    function_name=function_name,
    db_session=self.db,
    input_values=context_data,  # All available input values
    go_id=go_id,
    lo_id=lo_id,
    primary_entity_id=entity_id,
    primary_attribute_id=attribute_id
)
```

2. **System Function Execution** (in `_execute_system_function`):
```python
# OLD V1 WAY:
function_result = function_repository.auto_execute(function_name, self.db, **parameters)

# NEW V2 WAY:
from app.services.v2.v2_adapter import execute_system_function_v2
function_result = execute_system_function_v2(
    function_name=function_name,
    db_session=self.db,
    entity_id=entity_id,
    attribute_id=attribute_id,
    **input_values
)
```

## Available V2 Functions

### Database Functions
- **`fetch_records`**: Flexible record fetching with dynamic filtering
- **`create_record`**: Create new database records
- **`update_record`**: Update existing records
- **`delete_record`**: Delete records

### Utility Functions
- **`generate_id`**: Generate unique IDs with various strategies (UUID, sequential, custom)
- **`current_timestamp`**: Get current timestamp in various formats
- **`format_date`**: Format dates according to specifications

### Validation Functions
- **`validate_email`**: Email format validation
- **`validate_phone`**: Phone number validation
- **`validate_required`**: Required field validation

### Transform Functions
- **`transform_data`**: Data transformation utilities
- **`format_string`**: String formatting functions

### Math Functions
- **`calculate`**: Mathematical calculations
- **`sum_values`**: Sum numeric values

## Migration Strategy

### Phase 1: Critical Functions (CURRENT)
- ✅ `fetch_records` - Fixed parameter handling issues
- ✅ `generate_id` - Standardized ID generation
- 🔄 Function executor and adapter ready

### Phase 2: Gradual Migration
1. **Identify Problematic Functions**: Functions causing parameter errors
2. **Implement V2 Version**: Create V2 function following the pattern
3. **Update Function Registry**: Add to function_executor.py mappings
4. **Test Integration**: Verify with LO execution
5. **Switch to V2**: Update LO service to use V2 adapter

### Phase 3: Complete Migration
- Migrate all remaining functions to V2
- Remove V1 function repository dependencies
- Update all LO execution calls to use V2

## Testing the V2 System

### 1. Test Individual Functions
```python
from app.services.v2.function_executor import execute_v2_function

result = execute_v2_function(
    function_name="fetch_records",
    db_session=db,
    input_values={
        "table": "leave_types",
        "leave_type_name": "annual"
    }
)
```

### 2. Test via Adapter
```python
from app.services.v2.v2_adapter import execute_nested_function_v2

result = execute_nested_function_v2(
    function_name="generate_id",
    db_session=db,
    input_values={"entity": "LeaveApplication", "attribute": "leave_id"}
)
```

### 3. Test with LO Execution
- Use the existing V2 Execute API test
- Monitor logs for V2 function execution
- Verify no parameter mismatch errors

## Benefits of V2 Architecture

### 🚀 **Eliminates Parameter Issues**
- No more `unexpected keyword argument` errors
- Functions extract what they need from standardized input
- Dynamic parameter handling for flexible queries

### 🔧 **Better Debugging**
- Detailed execution logging with context
- Execution timing and performance metrics
- Structured error messages with error codes

### 📦 **Microservices Compliance**
- Each function is a separate, testable unit
- Clear separation of concerns
- Easy to add new functions

### 🔄 **Maintainability**
- Functions in separate files
- Standardized patterns across all functions
- Easy to understand and modify

### 🛡️ **Reliability**
- Comprehensive error handling
- Input validation
- Fallback mechanisms

## Next Steps

1. **Test Current Implementation**: Use the V2 Execute API to test fetch_records and generate_id
2. **Monitor Logs**: Check for successful V2 function execution
3. **Identify Next Functions**: Find other functions causing parameter issues
4. **Implement Additional Functions**: Create V2 versions following the established pattern
5. **Update LO Service**: Gradually switch to V2 adapter for all function calls

## File Structure
```
runtime/workflow-engine/app/services/v2/
├── __init__.py
├── models.py                    # Core data models
├── base_function.py            # Base function interface
├── function_executor.py        # Centralized function router
├── v2_adapter.py              # Integration adapter
├── functions/
│   ├── __init__.py
│   ├── database/
│   │   ├── fetch_records.py   # ✅ Implemented
│   │   ├── create_record.py   # 🔄 To implement
│   │   ├── update_record.py   # 🔄 To implement
│   │   └── delete_record.py   # 🔄 To implement
│   ├── utility/
│   │   ├── generate_id.py     # ✅ Implemented
│   │   ├── current_timestamp.py # 🔄 To implement
│   │   └── format_date.py     # 🔄 To implement
│   ├── validation/
│   ├── transform/
│   └── math/
└── V2_SYSTEM_FUNCTIONS_IMPLEMENTATION_GUIDE.md
```

This V2 architecture provides a robust, scalable, and maintainable solution for system function execution that eliminates the parameter mismatch issues while providing enhanced debugging and monitoring capabilities.
