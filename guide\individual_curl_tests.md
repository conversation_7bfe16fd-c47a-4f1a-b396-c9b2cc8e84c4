# Individual Curl Commands for Testing V2 Organizational APIs

## 🔧 **FIXED ISSUES VERIFICATION**

### **1. Test User Profile (Should now show department, team memberships, GO/LO/book/chapter permissions, and colleagues)**

```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/profile/U6" | jq '.'
```

**Expected Results:**
- ✅ `department` should show DEPT001 details (not null)
- ✅ `team_memberships` should show TEAM001 membership
- ✅ `global_objective_permissions` should show GO1 permissions
- ✅ `local_objective_permissions` should show LO1-LO5 permissions
- ✅ `book_permissions` should show b001 permissions
- ✅ `chapter_permissions` should show c001 permissions
- ✅ `team_colleagues` should show U8, U9
- ✅ `department_colleagues` should show U8, U9

---

## 🧪 **INDIVIDUAL API TESTS**

### **Authentication APIs**

#### 1. Health Check
```bash
curl -s -X GET "http://localhost:8000/api/v2/health" | jq '.'
```

#### 2. Register New User
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser_test",
    "email": "<EMAIL>",
    "password": "secure123",
    "first_name": "New",
    "last_name": "User",
    "roles": ["R1"],
    "tenant_id": "t001"
  }' | jq '.'
```

#### 3. Login User
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser_org",
    "password": "secure123"
  }' | jq '.'
```

#### 4. Get User by ID
```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/user/U6" | jq '.'
```

---

### **Organizational Structure APIs**

#### 5. Get All Departments
```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/departments" | jq '.'
```

#### 6. Get Specific Department
```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/departments/DEPT001" | jq '.'
```

#### 7. Get All Teams
```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/teams" | jq '.'
```

#### 8. Get Teams by Department
```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/teams?department_id=DEPT001" | jq '.'
```

#### 9. Get Specific Team
```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/teams/TEAM001" | jq '.'
```

---

### **User Profile & Team Management APIs**

#### 10. Update User Profile with Organizational Data
```bash
curl -s -X PUT "http://localhost:8000/api/v2/auth/profile/U6" \
  -H "Content-Type: application/json" \
  -d '{
    "department_id": "DEPT001",
    "organizational_level": "senior_level",
    "reports_to_user_id": "U7"
  }' | jq '.'
```

#### 11. Add User to Team
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/users/U6/teams" \
  -H "Content-Type: application/json" \
  -d '{
    "team_id": "TEAM002",
    "is_primary_team": false
  }' | jq '.'
```

#### 12. Remove User from Team
```bash
curl -s -X DELETE "http://localhost:8000/api/v2/auth/users/U6/teams/TEAM002" | jq '.'
```

---

### **Permission Testing APIs**

#### 13. Check Entity Permission (Employee can create leave application)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U6",
    "permission_type": "entity",
    "resource_identifier": "e1_leaveapplication",
    "action": "create"
  }' | jq '.'
```

#### 14. Check Attribute Permission (Employee can update email)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U6",
    "permission_type": "attribute",
    "resource_identifier": "e2_employee.email",
    "action": "update"
  }' | jq '.'
```

#### 15. Check Global Objective Permission (Employee can read GO1)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U6",
    "permission_type": "global_objective",
    "resource_identifier": "GO1",
    "action": "read"
  }' | jq '.'
```

#### 16. Check Local Objective Permission (Employee can submit leave request)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U6",
    "permission_type": "local_objective",
    "resource_identifier": "GO1.LO1",
    "action": "create"
  }' | jq '.'
```

#### 17. Check Book Permission (Employee can read book)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U6",
    "permission_type": "book",
    "resource_identifier": "b001",
    "action": "read"
  }' | jq '.'
```

#### 18. Check Chapter Permission (Employee can read chapter)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U6",
    "permission_type": "chapter",
    "resource_identifier": "c001",
    "action": "read"
  }' | jq '.'
```

#### 19. Check Denied Permission (Employee cannot approve leave)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U6",
    "permission_type": "local_objective",
    "resource_identifier": "GO1.LO4",
    "action": "create"
  }' | jq '.'
```

---

### **Manager Permission Tests**

#### 20. Check Manager Approval Permission (Manager can approve)
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/permissions/check" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U7",
    "permission_type": "local_objective",
    "resource_identifier": "GO1.LO4",
    "action": "create"
  }' | jq '.'
```

#### 21. Get Manager Profile (Should show different permissions)
```bash
curl -s -X GET "http://localhost:8000/api/v2/auth/profile/U7" | jq '.'
```

---

### **Token Management APIs**

#### 22. Refresh Token (Use refresh_token from login response)
```bash
# First login to get tokens
LOGIN_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser_org", "password": "secure123"}')

REFRESH_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.refresh_token')

# Then refresh
curl -s -X POST "http://localhost:8000/api/v2/auth/refresh" \
  -H "Content-Type: application/json" \
  -d "{\"refresh_token\": \"$REFRESH_TOKEN\"}" | jq '.'
```

#### 23. Logout User
```bash
curl -s -X POST "http://localhost:8000/api/v2/auth/logout" \
  -H "Content-Type: application/json" \
  -d "{\"refresh_token\": \"$REFRESH_TOKEN\"}" | jq '.'
```

---

## 🎯 **EXPECTED RESULTS SUMMARY**

### **Fixed Issues Verification:**
1. ✅ **Department**: Should show DEPT001 (Engineering) details
2. ✅ **Team Memberships**: Should show TEAM001 (Backend Development)
3. ✅ **GO Permissions**: Should show GO1 (Process Leave Requests)
4. ✅ **LO Permissions**: Should show LO1-LO5 (Submit, Upload, Review, Approve, Reject)
5. ✅ **Book Permissions**: Should show b001 (Employee Leave Management)
6. ✅ **Chapter Permissions**: Should show c001 (Leave Request Lifecycle)
7. ✅ **Team Colleagues**: Should show U8 (colleague1), U9 (colleague2)
8. ✅ **Department Colleagues**: Should show U8, U9

### **Permission Logic Verification:**
- **Employee (R1)**: Can submit/upload, cannot approve/reject
- **Manager (R2)**: Can review/approve/reject team members' requests
- **HRManager (R3)**: Full access to all leave management
- **SystemAdmin (R5)**: Complete system access

### **Row-Level Security:**
- **own_records_only**: Employee sees only their data
- **team_and_subordinates**: Manager sees team data
- **all**: Admin sees everything
