from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from bson import ObjectId
from app.database.postgres import Base
from sqlalchemy import Column, Integer, String, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

# MongoDB Document Models (using Pydantic)
class PyObjectId(ObjectId):
    """Custom type for MongoDB ObjectId"""
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")

class BaseWorkflowModel(BaseModel):
    """Base model for workflow-related documents"""
    id: Optional[PyObjectId] = Field(alias="_id", default=None)
    tenant_id: str = Field(..., description="Tenant identifier")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = Field(default=True)

class GlobalObjective(BaseWorkflowModel):
    """Represents a Global Objective in the workflow system"""
    objective_id: str = Field(..., description="Unique identifier for the objective")
    name: str = Field(..., description="Name of the global objective")
    description: str = Field(default="", description="Description of the global objective")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Configuration details")
    local_objectives: List[str] = Field(default_factory=list, description="List of associated Local Objectives")

class LocalObjective(BaseWorkflowModel):
    """Represents a Local Objective within a Global Objective"""
    objective_id: str = Field(..., description="Unique identifier for the local objective")
    global_objective_id: str = Field(..., description="Parent Global Objective ID")
    name: str = Field(..., description="Name of the local objective")
    description: str = Field(default="", description="Description of the local objective")
    order: int = Field(default=0, description="Execution order within the global objective")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Configuration details")
    nested_functions: List[str] = Field(default_factory=list, description="List of nested function IDs")

# PostgreSQL Relational Models
class WorkflowExecutionLog(Base):
    """Logs workflow execution details"""
    __tablename__ = "workflow_execution_logs"

    id = Column(Integer, primary_key=True, index=True)
    global_objective_id = Column(String, index=True)
    local_objective_id = Column(String, index=True)
    tenant_id = Column(String, index=True)
    status = Column(String)  # e.g., 'started', 'completed', 'failed'
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    execution_metadata = Column(JSON)

# Pydantic models for API interactions
class WorkflowExecutionCreate(BaseModel):
    """Input model for creating a workflow execution log"""
    global_objective_id: str
    local_objective_id: str
    tenant_id: str
    status: str
    execution_metadata: Optional[Dict[str, Any]] = None

class WorkflowExecutionResponse(WorkflowExecutionCreate):
    """Response model for workflow execution log"""
    id: int
    started_at: datetime
    completed_at: Optional[datetime] = None
