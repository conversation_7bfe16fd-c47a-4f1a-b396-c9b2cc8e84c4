"""
V2 Subtract Days Function

Math function to calculate the difference between two dates in days.
This replaces the problematic v1 subtract_days function with proper parameter handling.
"""

from typing import Dict, Any
from datetime import datetime, date

from ...base_function import BaseSystemFunction
from ...models import SystemFunctionInput, FunctionExecutionContext, FunctionCategory

class SubtractDaysFunction(BaseSystemFunction):
    """
    V2 Subtract Days function with standardized input handling.
    
    This function calculates the difference between two dates and returns the number of days.
    It dynamically accepts any two date parameters from the input stack.
    """
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.MATH
    
    def get_required_inputs(self) -> list[str]:
        """Required inputs for subtract_days function"""
        return []  # No hardcoded required inputs - will accept any date parameters
    
    def get_optional_inputs(self) -> list[str]:
        """Optional inputs for subtract_days function"""
        return [
            "date_format",              # Format of input dates if they're strings
            "include_end_date",         # Whether to include the end date in calculation
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> int:
        """
        Execute subtract_days with completely dynamic parameter handling.
        
        This function finds any two date parameters in the input and calculates the difference.
        """
        self.log_info("🔧 Starting subtract_days execution", context)
        
        # Get all input values and filter out non-date parameters
        all_inputs = input_data.input_values
        date_format = self.get_input_value(input_data, "date_format", "%Y-%m-%d")
        include_end_date = self.get_input_value(input_data, "include_end_date", True)
        
        # Find date parameters (exclude system parameters)
        date_values = []
        for key, value in all_inputs.items():
            if key not in ["date_format", "include_end_date", "entity", "attribute"] and value is not None:
                # Try to identify if this looks like a date
                if self._is_date_value(value, date_format):
                    date_values.append((key, value))
        
        self.log_info(f"📋 Found {len(date_values)} date parameters: {[k for k, v in date_values]}", context)
        
        if len(date_values) < 2:
            available_params = list(all_inputs.keys())
            self.log_error(f"Need at least 2 date parameters. Available: {available_params}", context)
            raise ValueError(f"Need at least 2 date parameters, found {len(date_values)}")
        
        # Use the first two date parameters found
        start_param, start_value = date_values[0]
        end_param, end_value = date_values[1]
        
        self.log_info(f"📋 Using {start_param}={start_value} and {end_param}={end_value}", context)
        
        # Convert to date objects if they're strings
        start_date = self._convert_to_date(start_value, date_format)
        end_date = self._convert_to_date(end_value, date_format)
        
        # Calculate difference
        difference = (end_date - start_date).days
        
        # Include end date if specified
        if include_end_date and difference >= 0:
            difference += 1
        
        self.log_info(f"✅ Calculated {difference} days between {start_param} and {end_param}", context)
        return difference
    
    def _is_date_value(self, value: Any, date_format: str) -> bool:
        """Check if a value looks like a date"""
        if isinstance(value, (date, datetime)):
            return True
        
        if isinstance(value, str):
            try:
                datetime.strptime(value, date_format)
                return True
            except (ValueError, TypeError):
                return False
        
        return False
    
    def _convert_to_date(self, value: Any, date_format: str) -> date:
        """Convert value to date object"""
        if isinstance(value, str):
            return datetime.strptime(value, date_format).date()
        elif isinstance(value, datetime):
            return value.date()
        elif isinstance(value, date):
            return value
        else:
            raise ValueError(f"Cannot convert {type(value)} to date: {value}")
