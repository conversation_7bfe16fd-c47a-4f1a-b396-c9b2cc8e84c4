# RBAC Implementation for Workflow System

This directory contains the Role-Based Access Control (RBAC) implementation for the Workflow System.

## Overview

The RBAC implementation provides authentication and permission services that enable secure access to workflows, entities, and functions based on user roles and permissions.

## Components

- **Authentication Service**: Provides user authentication, token generation and validation, and session management
- **Authentication Middleware**: Extracts tokens from requests, validates them, and populates the security context
- **Permission Service**: Evaluates permissions based on roles, organizational hierarchy, and context
- **API Endpoints**: Provides endpoints for authentication and permission checks

## Database Schema

The RBAC implementation uses the following database tables:

- `users`: User information
- `user_roles`: User role assignments
- `user_oauth_tokens`: OAuth tokens
- `user_sessions`: User sessions
- `roles`: Role definitions
- `permission_contexts`: Permission contexts
- `permission_types`: Permission types
- `role_permissions`: Role permission assignments
- `organizational_units`: Organizational units
- `user_organizations`: User organizational unit assignments
- `tenants`: Tenant information

## Integration

The RBAC implementation can be integrated into the workflow engine by:

1. Adding the authentication middleware to the FastAPI application
2. Including the authentication and permission routes
3. Using the authentication and permission dependencies in the routes

See the [INTEGRATION_PLAN.md](INTEGRATION_PLAN.md) file for a detailed integration plan.

## Files

- `auth_service.py`: Authentication service implementation
- `auth_middleware.py`: Authentication middleware implementation
- `auth_routes.py`: Authentication API endpoints
- `permission/permission_service.py`: Permission service implementation
- `permission/permission_routes.py`: Permission API endpoints
- `integration_example.py`: Example of how to integrate the authentication and permission services
- `rbac_schema_migration.sql`: SQL script for database schema updates
- `apply_migration.py`: Script to apply database schema migration
- `run_integration.sh`: Script to run the integration example
- `run_tests.sh`: Script to run the tests
- `test_auth.py`: Tests for the authentication and permission services
- `setup_test_db.py`: Script to set up the test database
- `requirements.txt`: Required packages for the RBAC implementation
- `IMPLEMENTATION_STATUS.md`: Current status of the RBAC implementation
- `INTEGRATION_PLAN.md`: Detailed plan for integrating the RBAC implementation

## Usage

### Running Tests

To run the tests, use the `run_tests.sh` script:

```bash
./run_tests.sh
```

### Running Integration Example

To run the integration example, use the `run_integration.sh` script:

```bash
./run_integration.sh
```

### Applying Database Migration

To apply the database schema migration, use the `apply_migration.py` script:

```bash
./apply_migration.py
```

You can also run it with the `--dry-run` flag to see the SQL statements without executing them:

```bash
./apply_migration.py --dry-run
```

## User and Role Entities

Users and roles are treated as first-class entities in the workflow engine, similar to other entities. This means they can be accessed in the input stack and can be updated like other entities.

### User Entity

The user entity has the following attributes:

- `user_id`: User ID (primary key)
- `username`: Username
- `email`: Email address
- `first_name`: First name
- `last_name`: Last name
- `status`: User status
- `roles`: List of roles assigned to the user

### Role Entity

The role entity has the following attributes:

- `role_id`: Role ID (primary key)
- `name`: Role name
- `description`: Role description
- `permissions`: List of permissions assigned to the role

## Session Management

The RBAC implementation includes session management for user authentication in a microservices architecture. Sessions are stored in the `user_sessions` table and can be validated using the `validate_session` function.

## Security Context

The security context is populated by the authentication middleware and contains the following information:

- `user_id`: User ID
- `username`: Username
- `roles`: List of roles assigned to the user
- `permissions`: List of permissions assigned to the user
- `org_units`: List of organizational units the user belongs to
- `tenant_id`: Tenant ID
- `authenticated`: Whether the user is authenticated
- `session_id`: Session ID

## Permission Evaluation

Permissions are evaluated based on:

- Direct permissions assigned to the user
- Role-based permissions
- Organizational hierarchy-based permissions
- Contextual permissions (e.g., ownership-based, time-based, attribute-based)

## Next Steps

See the [IMPLEMENTATION_STATUS.md](IMPLEMENTATION_STATUS.md) file for the current status of the RBAC implementation and the next steps.
