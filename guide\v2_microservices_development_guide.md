# V2 API Microservices Development Guide

This guide provides a standardized approach for developing microservices in the V2 API architecture. All future microservices (Local Objectives, etc.) should follow this pattern.

## Architecture Overview

The V2 API follows a microservices architecture where each business domain is organized into separate service modules:

```
app/api/v2/
├── auth/                           # Authentication microservice
│   ├── middleware.py              # Standardized V2 auth middleware
│   ├── models.py                  # Pydantic models
│   ├── service.py                 # Business logic
│   └── routes.py                  # FastAPI routes
├── global_objectives/             # Global Objectives microservice
│   ├── __init__.py               # Module exports
│   ├── models.py                 # Pydantic models
│   ├── service.py                # Business logic with RBAC
│   └── routes.py                 # FastAPI routes
└── api.py                        # Main V2 router
```

## Key Principles

### 1. Standardized Authentication & RBAC
- All microservices MUST use the V2 auth middleware (`app.api.v2.auth.middleware`)
- RBAC checks are implemented at the service layer
- Users only see data they have access to based on tenant membership and roles

### 2. Consistent Structure
Each microservice follows the same structure:
- `models.py` - Pydantic models for requests/responses
- `service.py` - Business logic with RBAC checks
- `routes.py` - FastAPI route definitions
- `__init__.py` - Module exports

### 3. Database Access Patterns
- Use SQLAlchemy with raw SQL for complex queries
- All queries filter by `tenant_id` and `deleted_mark = false`
- Service layer handles RBAC validation before data access

## Step-by-Step Implementation Guide

### Step 1: Create Microservice Directory

```bash
mkdir -p runtime/workflow-engine/app/api/v2/your_microservice
```

### Step 2: Create Models (`models.py`)

```python
"""
Your Microservice Models for v2 API
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class YourEntityResponse(BaseModel):
    """Your entity response model"""
    id: str = Field(..., description="Entity ID")
    name: str = Field(..., description="Entity name")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    # Add other fields as needed
    
    class Config:
        from_attributes = True

class YourEntityListResponse(BaseModel):
    """Your entity list response model"""
    entities: List[YourEntityResponse] = Field(..., description="List of entities")
    total_count: int = Field(..., description="Total count")
    filtered_count: int = Field(..., description="Count after filtering")

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
```

### Step 3: Create Service Layer (`service.py`)

```python
"""
Your Microservice Service for v2 API

This module contains the business logic with RBAC checks.
"""

import logging
from typing import Optional, List

from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from .models import YourEntityResponse, YourEntityListResponse

logger = logging.getLogger(__name__)

class RBACService:
    """Service class for Role-Based Access Control operations"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def check_user_permissions(self, user_id: str, tenant_id: str, resource: str, action: str) -> bool:
        """Check if user has permission to perform action on resource."""
        try:
            # Get user roles
            query = """
            SELECT DISTINCT ur.role
            FROM workflow_runtime.user_roles ur
            WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
            """
            
            result = self.db.execute(text(query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchall()
            
            if not result:
                return False
            
            user_roles = [row[0] for row in result]
            
            # Check permissions based on roles
            if "Administrator" in user_roles:
                return True
            if "Manager" in user_roles and action in ["read", "write"]:
                return True
            if "User" in user_roles and action == "read":
                return True
            if "Viewer" in user_roles and action == "read":
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking user permissions: {str(e)}")
            return False
    
    def check_tenant_access(self, user_id: str, tenant_id: str) -> bool:
        """Check if user has access to the specified tenant."""
        try:
            query = """
            SELECT COUNT(*)
            FROM workflow_runtime.user_roles ur
            WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
            """
            
            result = self.db.execute(text(query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            return result[0] > 0 if result else False
            
        except Exception as e:
            self.logger.error(f"Error checking tenant access: {str(e)}")
            return False

class YourMicroserviceService:
    """Service class for your microservice operations"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
        self.rbac = RBACService(db_session)
    
    def get_entities(
        self, 
        user_id: str, 
        tenant_id: str,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> Optional[YourEntityListResponse]:
        """Get entities with RBAC checks."""
        try:
            # Check RBAC permissions
            if not self.rbac.check_user_permissions(user_id, tenant_id, "your_resource", "read"):
                self.logger.warning(f"User {user_id} denied read access")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            # Build query with tenant filtering
            base_query = """
            SELECT id, name, tenant_id, created_at
            FROM workflow_runtime.your_table
            WHERE tenant_id = :tenant_id AND deleted_mark = false
            ORDER BY id
            """
            
            count_query = """
            SELECT COUNT(*)
            FROM workflow_runtime.your_table
            WHERE tenant_id = :tenant_id AND deleted_mark = false
            """
            
            params = {"tenant_id": tenant_id.lower()}
            
            # Get total count
            total_count = self.db.execute(text(count_query), params).fetchone()[0]
            
            # Add pagination
            if limit:
                base_query += " LIMIT :limit"
                params["limit"] = limit
            
            if offset:
                base_query += " OFFSET :offset"
                params["offset"] = offset
            
            # Execute query
            result = self.db.execute(text(base_query), params).fetchall()
            
            # Build response objects
            entities = [
                YourEntityResponse(
                    id=row.id,
                    name=row.name,
                    tenant_id=row.tenant_id,
                    created_at=row.created_at
                )
                for row in result
            ]
            
            return YourEntityListResponse(
                entities=entities,
                total_count=total_count,
                filtered_count=len(entities)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting entities: {str(e)}")
            return None
```

### Step 4: Create Routes (`routes.py`)

```python
"""
Your Microservice Routes for v2 API

This module contains the FastAPI routes with RBAC checks.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional

from app.db.session import get_db
from ..auth.middleware import get_security_context, require_auth, SecurityContext
from .models import YourEntityListResponse, ErrorResponse
from .service import YourMicroserviceService

# Create router
router = APIRouter(
    prefix="/your_microservice",
    tags=["your-microservice-v2"],
    responses={
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Forbidden"},
        404: {"model": ErrorResponse, "description": "Not Found"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)

@router.get(
    "/",
    response_model=YourEntityListResponse,
    summary="Get all entities",
    description="Retrieve all entities for the authenticated user's tenant with RBAC checks"
)
async def get_all_entities(
    tenant_id: str = Query(..., description="Tenant ID to filter entities"),
    limit: Optional[int] = Query(None, description="Limit number of results", ge=1, le=1000),
    offset: Optional[int] = Query(None, description="Offset for pagination", ge=0),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> YourEntityListResponse:
    """Get all entities for the authenticated user's tenant."""
    try:
        service = YourMicroserviceService(db)
        
        result = service.get_entities(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            limit=limit,
            offset=offset
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access entities",
                    "details": {}
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error getting entities: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving entities",
                "details": {}
            }
        )
```

### Step 5: Create Module Exports (`__init__.py`)

```python
"""
Your Microservice v2 API Module
"""

from .routes import router
from .models import YourEntityResponse, YourEntityListResponse, ErrorResponse
from .service import YourMicroserviceService

__all__ = [
    "router",
    "YourEntityResponse", 
    "YourEntityListResponse",
    "ErrorResponse",
    "YourMicroserviceService"
]
```

### Step 6: Register in Main Router

Add your microservice to `app/api/v2/api.py`:

```python
from .your_microservice.routes import router as your_microservice_router

# Include microservice routers
api_router.include_router(
    your_microservice_router,
    tags=["Your Microservice v2"]
)
```

## RBAC Implementation

### Supported Roles
- **Administrator**: Full access (read, write, delete)
- **Manager**: Read and write access
- **User**: Read access only
- **Viewer**: Read access only

### Permission Checks
1. **User Authentication**: Verified via JWT token
2. **Tenant Access**: User must belong to the requested tenant
3. **Role-Based Permissions**: Based on user's roles in the tenant
4. **Data Filtering**: All queries automatically filter by tenant and deleted_mark

## Testing Your Microservice

### 1. Authentication Test
```bash
# Login to get token
curl -X POST "http://localhost:8000/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "testpass"}'
```

### 2. Endpoint Test
```bash
# Test your endpoint
curl -X GET "http://localhost:8000/api/v2/your_microservice/?tenant_id=t001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. RBAC Test
- Test with different user roles (Administrator, Manager, User, Viewer)
- Test with users from different tenants
- Verify access denied for unauthorized users

## Database Schema Requirements

Your microservice tables should include:
- `tenant_id` (VARCHAR) - For multi-tenancy
- `deleted_mark` (BOOLEAN) - For soft deletes
- `created_at` (TIMESTAMP) - Creation timestamp
- `updated_at` (TIMESTAMP) - Update timestamp
- `created_by` (VARCHAR) - User who created the record
- `updated_by` (VARCHAR) - User who last updated the record

## Error Handling Standards

### HTTP Status Codes
- `200` - Success
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

### Error Response Format
```json
{
  "error": "ERROR_TYPE",
  "message": "Human readable message",
  "details": {
    "additional": "context"
  }
}
```

## Best Practices

1. **Always use RBAC checks** in service layer before data access
2. **Filter by tenant_id** in all database queries
3. **Use soft deletes** with `deleted_mark = false`
4. **Implement pagination** for list endpoints
5. **Log security events** (access denied, etc.)
6. **Use consistent error responses** across all endpoints
7. **Include comprehensive API documentation** with examples
8. **Test with different user roles** and tenants

## Example Implementation

The Global Objectives microservice (`app/api/v2/global_objectives/`) serves as a complete reference implementation following all these patterns.

## Future Enhancements

- Add caching layer for frequently accessed data
- Implement audit logging for all operations
- Add rate limiting per tenant/user
- Support for field-level permissions
- Integration with external identity providers
