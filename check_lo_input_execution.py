#!/usr/bin/env python3
"""
Script to check lo_input_execution table and diagnose NULL value issues
"""

import sys
import os
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the app directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_connection():
    """Get database connection using the app's configuration"""
    try:
        from core.config import settings
        
        # Build connection string
        connection_string = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
        
        engine = create_engine(connection_string)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        return SessionLocal()
        
    except Exception as e:
        logger.error(f"Failed to connect to database: {str(e)}")
        return None

def check_table_structure(db):
    """Check the structure of lo_input_execution table"""
    logger.info("🔍 Checking lo_input_execution table structure...")
    
    try:
        query = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'workflow_runtime' 
        AND table_name = 'lo_input_execution'
        ORDER BY ordinal_position
        """
        
        result = db.execute(text(query)).fetchall()
        
        if result:
            logger.info("✅ Table structure:")
            for row in result:
                logger.info(f"   - {row.column_name}: {row.data_type} (nullable: {row.is_nullable}, default: {row.column_default})")
        else:
            logger.error("❌ Table not found or no columns returned")
            
    except Exception as e:
        logger.error(f"❌ Error checking table structure: {str(e)}")

def check_recent_records(db):
    """Check recent records in lo_input_execution table"""
    logger.info("\n🔍 Checking recent records in lo_input_execution...")
    
    try:
        query = """
        SELECT instance_id, lo_id, input_contextual_id, 
               input_value, 
               CASE 
                   WHEN input_value IS NULL THEN 'NULL'
                   ELSE 'HAS_VALUE'
               END as value_status,
               created_at, updated_at, updated_by
        FROM workflow_runtime.lo_input_execution 
        ORDER BY COALESCE(updated_at, created_at) DESC 
        LIMIT 10
        """
        
        result = db.execute(text(query)).fetchall()
        
        if result:
            logger.info(f"✅ Found {len(result)} recent records:")
            for i, row in enumerate(result, 1):
                logger.info(f"   {i}. Instance: {row.instance_id}")
                logger.info(f"      LO: {row.lo_id}")
                logger.info(f"      Context: {row.input_contextual_id}")
                logger.info(f"      Value Status: {row.value_status}")
                if row.value_status == 'HAS_VALUE':
                    logger.info(f"      Value: {row.input_value}")
                logger.info(f"      Updated: {row.updated_at} by {row.updated_by}")
                logger.info("")
        else:
            logger.warning("⚠️ No records found in lo_input_execution table")
            
    except Exception as e:
        logger.error(f"❌ Error checking recent records: {str(e)}")

def check_null_records(db):
    """Check specifically for NULL records"""
    logger.info("🔍 Checking for NULL input_value records...")
    
    try:
        query = """
        SELECT COUNT(*) as null_count,
               COUNT(CASE WHEN input_value IS NOT NULL THEN 1 END) as non_null_count,
               COUNT(*) as total_count
        FROM workflow_runtime.lo_input_execution
        """
        
        result = db.execute(text(query)).fetchone()
        
        if result:
            logger.info(f"📊 Record statistics:")
            logger.info(f"   - Total records: {result.total_count}")
            logger.info(f"   - NULL values: {result.null_count}")
            logger.info(f"   - Non-NULL values: {result.non_null_count}")
            
            if result.null_count > 0:
                logger.warning(f"⚠️ Found {result.null_count} NULL values - this is the issue!")
                
                # Get some examples of NULL records
                null_examples_query = """
                SELECT instance_id, lo_id, input_contextual_id, created_at, updated_at
                FROM workflow_runtime.lo_input_execution 
                WHERE input_value IS NULL
                ORDER BY COALESCE(updated_at, created_at) DESC
                LIMIT 5
                """
                
                null_examples = db.execute(text(null_examples_query)).fetchall()
                logger.info("   Examples of NULL records:")
                for example in null_examples:
                    logger.info(f"     - {example.instance_id} | {example.lo_id} | {example.input_contextual_id}")
            else:
                logger.info("✅ No NULL values found!")
                
    except Exception as e:
        logger.error(f"❌ Error checking NULL records: {str(e)}")

def test_jsonb_insert(db):
    """Test JSONB insertion to verify our approach works"""
    logger.info("\n🧪 Testing JSONB insertion...")
    
    try:
        # Test different value types
        test_values = [
            ("test_string", "hello world"),
            ("test_number", 42),
            ("test_boolean", True),
            ("test_object", {"key": "value", "number": 123}),
            ("test_array", [1, 2, 3, "four"]),
        ]
        
        for test_name, test_value in test_values:
            logger.info(f"   Testing {test_name}: {test_value} (type: {type(test_value)})")
            
            # Create a test record
            insert_query = """
            INSERT INTO workflow_runtime.lo_input_execution 
            (instance_id, lo_id, input_contextual_id, input_value, created_at, created_by)
            VALUES (:instance_id, :lo_id, :contextual_id, :value, NOW(), 'test_user')
            ON CONFLICT (instance_id, lo_id, input_contextual_id) 
            DO UPDATE SET input_value = EXCLUDED.input_value, updated_at = NOW()
            """
            
            db.execute(text(insert_query), {
                "instance_id": "test-instance-jsonb",
                "lo_id": "test-lo-jsonb", 
                "contextual_id": test_name,
                "value": test_value
            })
            
            # Verify the insertion
            verify_query = """
            SELECT input_value FROM workflow_runtime.lo_input_execution
            WHERE instance_id = 'test-instance-jsonb' 
            AND lo_id = 'test-lo-jsonb' 
            AND input_contextual_id = :contextual_id
            """
            
            result = db.execute(text(verify_query), {"contextual_id": test_name}).fetchone()
            
            if result and result.input_value is not None:
                logger.info(f"     ✅ Successfully stored: {result.input_value}")
            else:
                logger.error(f"     ❌ Failed to store or returned NULL")
        
        # Commit the test
        db.commit()
        logger.info("✅ JSONB test completed")
        
    except Exception as e:
        logger.error(f"❌ JSONB test failed: {str(e)}")
        db.rollback()

def main():
    """Run all diagnostic checks"""
    logger.info("🚀 Starting lo_input_execution diagnostic checks...")
    
    db = get_database_connection()
    if not db:
        logger.error("❌ Could not connect to database")
        return False
    
    try:
        check_table_structure(db)
        check_recent_records(db)
        check_null_records(db)
        test_jsonb_insert(db)
        
        logger.info("\n🎯 Diagnostic Summary:")
        logger.info("✅ All diagnostic checks completed")
        logger.info("📋 Check the logs above for any issues with NULL values")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Diagnostic failed: {str(e)}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
