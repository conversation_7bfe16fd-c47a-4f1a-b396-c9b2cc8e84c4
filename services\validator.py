from typing import Dict, Any, List, Optional, Union
import re
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Exception raised for validation errors."""
    def __init__(self, field, message):
        self.field = field
        self.message = message
        super().__init__(f"Validation error for field '{field}': {message}")

class Validator:
    """
    Validates data against schema definitions.
    Used for input/output stack validation.
    """
    
    def validate(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data against schema.
        
        Args:
            data: Data to validate
            schema: Validation schema
            
        Returns:
            Validated and possibly transformed data
            
        Raises:
            ValidationError: If validation fails
        """
        if not schema:
            return data.copy() if data else {}
            
        # Create a copy of the data for modification
        validated_data = data.copy() if data else {}
        
        # Check required fields
        required_fields = schema.get("required", [])
        for field in required_fields:
            if field not in validated_data or validated_data[field] is None or validated_data[field] == "":
                raise ValidationError(field, "Field is required")
        
        # Validate properties
        properties = schema.get("properties", {})
        for field, field_schema in properties.items():
            # Skip validation if field not present (unless required)
            if field not in validated_data or validated_data[field] is None:
                # Apply default value if specified
                if "default" in field_schema and field not in validated_data:
                    validated_data[field] = field_schema["default"]
                continue
            
            # Get the current value
            value = validated_data[field]
            
            # Validate type
            if "type" in field_schema:
                self._validate_type(field, value, field_schema["type"])
            
            # Validate format
            if "format" in field_schema:
                validated_data[field] = self._validate_format(field, value, field_schema["format"])
            
            # Validate enum
            if "enum" in field_schema and value not in field_schema["enum"]:
                raise ValidationError(field, f"Value must be one of: {', '.join(map(str, field_schema['enum']))}")
            
            # Validate minimum/maximum for numbers
            if field_schema.get("type") in ["number", "integer"]:
                if "minimum" in field_schema and value < field_schema["minimum"]:
                    raise ValidationError(field, f"Value must be greater than or equal to {field_schema['minimum']}")
                    
                if "maximum" in field_schema and value > field_schema["maximum"]:
                    raise ValidationError(field, f"Value must be less than or equal to {field_schema['maximum']}")
            
            # Validate minLength/maxLength for strings
            if field_schema.get("type") == "string":
                if "minLength" in field_schema and len(value) < field_schema["minLength"]:
                    raise ValidationError(field, f"String length must be at least {field_schema['minLength']}")
                    
                if "maxLength" in field_schema and len(value) > field_schema["maxLength"]:
                    raise ValidationError(field, f"String length must be at most {field_schema['maxLength']}")
                    
                if "pattern" in field_schema and not re.match(field_schema["pattern"], value):
                    raise ValidationError(field, "Value does not match the required pattern")
            
            # Validate minItems/maxItems for arrays
            if field_schema.get("type") == "array":
                if "minItems" in field_schema and len(value) < field_schema["minItems"]:
                    raise ValidationError(field, f"Array must have at least {field_schema['minItems']} items")
                    
                if "maxItems" in field_schema and len(value) > field_schema["maxItems"]:
                    raise ValidationError(field, f"Array must have at most {field_schema['maxItems']} items")
                    
                # Validate array items if items schema is provided
                if "items" in field_schema and value:
                    items_schema = field_schema["items"]
                    for i, item in enumerate(value):
                        if items_schema.get("type"):
                            self._validate_type(f"{field}[{i}]", item, items_schema["type"])
                        
                        # For objects in arrays, recursively validate
                        if items_schema.get("type") == "object" and "properties" in items_schema:
                            value[i] = self.validate(item, items_schema)
        
        # Check for additional properties
        if schema.get("additionalProperties") is False:
            extra_fields = set(validated_data.keys()) - set(properties.keys())
            if extra_fields:
                raise ValidationError(
                    list(extra_fields)[0],
                    "Additional properties are not allowed"
                )
        
        return validated_data
    
    def _validate_type(self, field: str, value: Any, expected_type: str) -> None:
        """
        Validate value type.
        
        Args:
            field: Field name for error messages
            value: Value to validate
            expected_type: Expected type
            
        Raises:
            ValidationError: If type validation fails
        """
        if expected_type == "string" and not isinstance(value, str):
            raise ValidationError(field, "Must be a string")
            
        elif expected_type == "number" and not isinstance(value, (int, float)):
            raise ValidationError(field, "Must be a number")
            
        elif expected_type == "integer" and not isinstance(value, int):
            raise ValidationError(field, "Must be an integer")
            
        elif expected_type == "boolean" and not isinstance(value, bool):
            raise ValidationError(field, "Must be a boolean")
            
        elif expected_type == "array" and not isinstance(value, list):
            raise ValidationError(field, "Must be an array")
            
        elif expected_type == "object" and not isinstance(value, dict):
            raise ValidationError(field, "Must be an object")
    
    def _validate_format(self, field: str, value: Any, format_name: str) -> Any:
        """
        Validate and format value according to format specification.
        
        Args:
            field: Field name for error messages
            value: Value to validate
            format_name: Format specification
            
        Returns:
            Possibly transformed value
            
        Raises:
            ValidationError: If format validation fails
        """
        if format_name == "date":
            if isinstance(value, datetime):
                return value.date().isoformat()
            elif isinstance(value, str):
                try:
                    return datetime.fromisoformat(value).date().isoformat()
                except ValueError:
                    raise ValidationError(field, "Must be a valid ISO date format (YYYY-MM-DD)")
        
        elif format_name == "date-time":
            if isinstance(value, datetime):
                return value.isoformat()
            elif isinstance(value, str):
                try:
                    return datetime.fromisoformat(value.replace("Z", "+00:00")).isoformat()
                except ValueError:
                    raise ValidationError(field, "Must be a valid ISO date-time format")
        
        elif format_name == "email":
            if not isinstance(value, str):
                raise ValidationError(field, "Email must be a string")
                
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_regex, value):
                raise ValidationError(field, "Must be a valid email address")
        
        elif format_name == "uri":
            if not isinstance(value, str):
                raise ValidationError(field, "URI must be a string")
                
            uri_regex = r'^(https?|ftp)://[^\s/$.?#].[^\s]*$'
            if not re.match(uri_regex, value):
                raise ValidationError(field, "Must be a valid URI")
                
        return value
