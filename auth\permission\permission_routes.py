"""
Permission Routes for the Workflow System.

This module provides API endpoints for permission checks:
- Single permission check endpoint
- Bulk permission check endpoint
- User permissions listing endpoint
"""

from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.db.session import get_db
from app.auth.auth_middleware import get_security_context, require_auth, SecurityContext
from app.auth.permission.permission_service import (
    PermissionService, 
    PermissionContext, 
    PermissionType, 
    ResourceType
)

# Create router
router = APIRouter(
    prefix="/permissions",
    tags=["permissions"],
    responses={401: {"description": "Unauthorized"}, 403: {"description": "Forbidden"}},
)

# Models
class PermissionRequest(BaseModel):
    """Permission check request model."""
    resource_type: ResourceType
    resource_id: str
    permission_type: PermissionType
    tenant_id: Optional[str] = None
    org_unit_id: Optional[str] = None
    additional_context: Optional[Dict[str, Any]] = None

class PermissionResponse(BaseModel):
    """Permission check response model."""
    resource_type: ResourceType
    resource_id: str
    permission_type: PermissionType
    granted: bool

class BulkPermissionRequest(BaseModel):
    """Bulk permission check request model."""
    permissions: List[PermissionRequest]

class BulkPermissionResponse(BaseModel):
    """Bulk permission check response model."""
    results: Dict[str, bool]

class UserPermission(BaseModel):
    """User permission model."""
    context_id: str
    name: str
    context_type: str
    permission_id: str
    resource_id: Optional[str] = None
    conditions: Optional[Dict[str, Any]] = None

@router.post("/check", response_model=PermissionResponse)
async def check_permission(
    request: PermissionRequest,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Check if the current user has permission to perform an action on a resource.
    
    Args:
        request: Permission check request
        security_context: Security context with user information
        db: Database session
        
    Returns:
        PermissionResponse: Permission check result
    """
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=request.resource_type,
        resource_id=request.resource_id,
        permission_type=request.permission_type,
        tenant_id=request.tenant_id,
        org_unit_id=request.org_unit_id,
        additional_context=request.additional_context
    )
    
    # Check permission
    granted = permission_service.check_permission(security_context, permission_context)
    
    return PermissionResponse(
        resource_type=request.resource_type,
        resource_id=request.resource_id,
        permission_type=request.permission_type,
        granted=granted
    )

@router.post("/check-bulk", response_model=BulkPermissionResponse)
async def check_permissions_bulk(
    request: BulkPermissionRequest,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Check multiple permissions at once.
    
    Args:
        request: Bulk permission check request
        security_context: Security context with user information
        db: Database session
        
    Returns:
        BulkPermissionResponse: Bulk permission check results
    """
    permission_service = PermissionService(db)
    
    # Create permission contexts
    permission_contexts = [
        PermissionContext(
            resource_type=perm.resource_type,
            resource_id=perm.resource_id,
            permission_type=perm.permission_type,
            tenant_id=perm.tenant_id,
            org_unit_id=perm.org_unit_id,
            additional_context=perm.additional_context
        )
        for perm in request.permissions
    ]
    
    # Check permissions
    results = permission_service.check_permissions(security_context, permission_contexts)
    
    return BulkPermissionResponse(results=results)

@router.get("/my-permissions", response_model=List[UserPermission])
async def get_my_permissions(
    resource_type: Optional[ResourceType] = None,
    resource_id: Optional[str] = None,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Get all permissions for the current user.
    
    Args:
        resource_type: Optional resource type to filter by
        resource_id: Optional resource ID to filter by
        security_context: Security context with user information
        db: Database session
        
    Returns:
        List[UserPermission]: List of user permissions
    """
    permission_service = PermissionService(db)
    
    # Get user permissions
    permissions = permission_service.get_user_permissions(
        security_context,
        resource_type=resource_type,
        resource_id=resource_id
    )
    
    return [UserPermission(**perm) for perm in permissions]

@router.post("/require", status_code=status.HTTP_204_NO_CONTENT)
async def require_permission(
    request: PermissionRequest,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Require a permission, raising an exception if not granted.
    
    Args:
        request: Permission check request
        security_context: Security context with user information
        db: Database session
        
    Returns:
        None
        
    Raises:
        HTTPException: If the user does not have the required permission
    """
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=request.resource_type,
        resource_id=request.resource_id,
        permission_type=request.permission_type,
        tenant_id=request.tenant_id,
        org_unit_id=request.org_unit_id,
        additional_context=request.additional_context
    )
    
    # Require permission
    permission_service.require_permission(security_context, permission_context)
    
    return None
