from fastapi import APIRouter
from app.api.v1.endpoints import workflows, entities, config, chat, database, global_objectives, my_transaction
from app.api.v1.endpoints import claude
from app.auth.auth_routes import router as auth_router
from app.auth.permission.permission_routes import router as permission_router

api_router = APIRouter()
api_router.include_router(workflows.router, prefix="/workflows", tags=["workflows"])
api_router.include_router(entities.router, prefix="/entities", tags=["entities"])
api_router.include_router(config.router, prefix="/config", tags=["config"])
api_router.include_router(chat.router, prefix="/chat", tags=["chat"])
api_router.include_router(database.router, prefix="/database", tags=["database"])
api_router.include_router(global_objectives.router, prefix="/global-objectives", tags=["Global Objectives"])
api_router.include_router(claude.router, prefix="/claude", tags=["claude"])
api_router.include_router(my_transaction.router, prefix="/my_transaction", tags=["My Transaction"])

# Include authentication and permission routers
api_router.include_router(auth_router, prefix="/auth", tags=["authentication"])
api_router.include_router(permission_router, prefix="/permissions", tags=["permissions"])
