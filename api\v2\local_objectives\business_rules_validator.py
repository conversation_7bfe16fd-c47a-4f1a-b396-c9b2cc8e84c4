"""
Business Rules Validator for LO Execution

This module handles business rule validation during LO execution.
Business rules are executed as nested functions with BUSINESS_RULE type.
"""

import logging
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

logger = logging.getLogger(__name__)

class BusinessRulesValidator:
    """Service for executing business rule validations during LO execution"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def validate_business_rules(
        self, 
        lo_id: str, 
        instance_id: str, 
        processed_inputs: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """
        Execute all business rules for the given LO and return validation results.
        
        Args:
            lo_id: Local Objective ID
            instance_id: Workflow instance ID
            processed_inputs: All processed input values
            user_id: User executing the LO
            
        Returns:
            Dict containing validation results and any error messages
        """
        try:
            self.logger.info(f"🔍 BUSINESS RULES: Starting validation for LO {lo_id}")
            
            # Get all business rule nested functions for this LO
            business_rules = self._get_business_rules_for_lo(lo_id)
            
            if not business_rules:
                self.logger.info(f"🔍 BUSINESS RULES: No business rules found for LO {lo_id}")
                return {"validation_passed": True, "validation_result": "success"}
            
            self.logger.info(f"🔍 BUSINESS RULES: Found {len(business_rules)} business rules to execute")
            
            validation_results = []
            overall_validation_passed = True
            
            # Execute each business rule
            for rule in business_rules:
                rule_result = self._execute_business_rule(
                    rule, instance_id, processed_inputs, user_id
                )
                
                validation_results.append(rule_result)
                
                # If any rule fails, overall validation fails
                if not rule_result.get("validation_passed", True):
                    overall_validation_passed = False
                    self.logger.warning(f"🔍 BUSINESS RULES: Rule {rule['nested_function_id']} failed: {rule_result.get('error_message')}")
            
            # Determine final validation result
            final_result = {
                "validation_passed": overall_validation_passed,
                "validation_result": "success" if overall_validation_passed else "failure",
                "business_rule_results": validation_results
            }
            
            if not overall_validation_passed:
                # Collect all error messages
                error_messages = [r.get("error_message") for r in validation_results if not r.get("validation_passed")]
                final_result["error_messages"] = error_messages
                final_result["primary_error"] = error_messages[0] if error_messages else "Business rule validation failed"
            
            self.logger.info(f"🔍 BUSINESS RULES: Overall validation result: {final_result['validation_result']}")
            return final_result
            
        except Exception as e:
            self.logger.error(f"🔍 BUSINESS RULES: Error during validation: {str(e)}")
            return {
                "validation_passed": False,
                "validation_result": "failure",
                "error_messages": [f"Business rule validation error: {str(e)}"],
                "primary_error": f"Business rule validation error: {str(e)}"
            }
    
    def _get_business_rules_for_lo(self, lo_id: str) -> List[Dict[str, Any]]:
        """Get all business rule nested functions mapped to this LO"""
        try:
            query = """
            SELECT DISTINCT nf.nested_function_id, nf.function_name, nf.description,
                   nf.success_message, nf.error_message
            FROM workflow_runtime.lo_nested_functions nf
            JOIN workflow_runtime.lo_nested_function_mappings nfm ON nf.nested_function_id = nfm.nested_function_id
            WHERE nf.function_type = 'BUSINESS_RULE' 
            AND nfm.lo_id = :lo_id
            ORDER BY nf.nested_function_id
            """
            
            results = self.db.execute(text(query), {"lo_id": lo_id}).fetchall()
            
            business_rules = []
            for row in results:
                business_rules.append({
                    "nested_function_id": row.nested_function_id,
                    "function_name": row.function_name,
                    "description": row.description,
                    "success_message": row.success_message,
                    "error_message": row.error_message
                })
            
            return business_rules
            
        except Exception as e:
            self.logger.error(f"Error getting business rules for LO {lo_id}: {str(e)}")
            return []
    
    def _execute_business_rule(
        self, 
        rule: Dict[str, Any], 
        instance_id: str, 
        processed_inputs: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """Execute a single business rule and return validation result"""
        try:
            nested_function_id = rule["nested_function_id"]
            self.logger.info(f"🔍 BUSINESS RULES: Executing rule {nested_function_id}")
            
            # Populate nested function inputs with current processed inputs
            self._populate_business_rule_inputs(nested_function_id, instance_id, processed_inputs, user_id)
            
            # Execute the business rule (conditional_assignment function)
            result = self._execute_nested_function(nested_function_id, instance_id)
            
            # Interpret the result
            validation_passed = True
            error_message = None
            
            if result == "error" or result == "failure":
                validation_passed = False
                error_message = rule.get("error_message", "Business rule validation failed")
            elif result == "success":
                validation_passed = True
            else:
                # Handle other result types
                if isinstance(result, dict):
                    validation_passed = result.get("validation_result") == "success"
                    if not validation_passed:
                        error_message = result.get("message", rule.get("error_message", "Business rule validation failed"))
                else:
                    # For any other string result, check if it indicates failure
                    if str(result).lower() in ["error", "failure", "false", "fail"]:
                        validation_passed = False
                        error_message = rule.get("error_message", "Business rule validation failed")
                    else:
                        validation_passed = True
            
            # Update the entity's validationResult field
            self._update_validation_result(instance_id, "success" if validation_passed else "failure")
            
            return {
                "rule_id": nested_function_id,
                "validation_passed": validation_passed,
                "result": result,
                "error_message": error_message,
                "success_message": rule.get("success_message") if validation_passed else None
            }
            
        except Exception as e:
            self.logger.error(f"Error executing business rule {rule['nested_function_id']}: {str(e)}")
            return {
                "rule_id": rule["nested_function_id"],
                "validation_passed": False,
                "result": None,
                "error_message": f"Business rule execution error: {str(e)}"
            }
    
    def _populate_business_rule_inputs(
        self, 
        nested_function_id: str, 
        instance_id: str, 
        processed_inputs: Dict[str, Any],
        user_id: str
    ):
        """Populate business rule nested function inputs with current data"""
        try:
            # Get business rule input parameter definitions
            params_query = """
            SELECT item_id, slot_id, entity_id, attribute_id, value, data_type
            FROM workflow_runtime.lo_nested_function_input_items
            WHERE nested_function_id = :nested_function_id
            ORDER BY item_id
            """
            
            param_results = self.db.execute(text(params_query), {"nested_function_id": nested_function_id}).fetchall()
            
            for param_row in param_results:
                slot_id = param_row.slot_id
                item_id = param_row.item_id
                default_value = param_row.value
                
                self.logger.info(f"🔍 BUSINESS RULES: Processing parameter {slot_id}")
                
                # Determine the input value
                input_value = None
                
                if slot_id == "LeaveApplication.leaveTypeName":
                    # This is the actual input parameter - get from processed inputs
                    attribute_id = param_row.attribute_id
                    
                    if attribute_id in processed_inputs:
                        input_value = processed_inputs[attribute_id]
                        self.logger.info(f"🔍 BUSINESS RULES: Found leaveTypeName value: {input_value}")
                    else:
                        self.logger.warning(f"🔍 BUSINESS RULES: No value found for leaveTypeName {attribute_id}")
                        
                elif slot_id == "LeaveApplication.startDate":
                    # This is the actual input parameter - get from processed inputs
                    attribute_id = param_row.attribute_id
                    
                    if attribute_id in processed_inputs:
                        input_value = processed_inputs[attribute_id]
                        self.logger.info(f"🔍 BUSINESS RULES: Found startDate value: {input_value}")
                    else:
                        self.logger.warning(f"🔍 BUSINESS RULES: No value found for startDate {attribute_id}")
                        
                else:
                    # For other parameters, use the default value from the database
                    input_value = default_value
                    self.logger.info(f"🔍 BUSINESS RULES: Using default value for {slot_id}: {input_value}")
                
                # Insert/Update the nested function input execution record
                if input_value is not None:
                    self._upsert_nested_function_input(
                        instance_id, nested_function_id, slot_id, input_value, user_id
                    )
                
        except Exception as e:
            self.logger.error(f"Error populating business rule inputs: {str(e)}")
    
    def _upsert_nested_function_input(
        self, 
        instance_id: str, 
        nested_function_id: str, 
        slot_id: str, 
        input_value: Any,
        user_id: str
    ):
        """Insert or update nested function input execution record"""
        try:
            import json
            
            # JSON encode the input_value for JSONB field
            if input_value is not None:
                try:
                    json_encoded_value = json.dumps(input_value)
                except (TypeError, ValueError):
                    json_encoded_value = json.dumps(str(input_value))
            else:
                json_encoded_value = None
            
            upsert_query = """
            INSERT INTO workflow_runtime.lo_nested_function_input_execution 
            (instance_id, nested_function_id, input_contextual_id, input_value, created_at, created_by, updated_at, updated_by)
            VALUES (:instance_id, :nested_function_id, :input_contextual_id, :input_value, NOW(), :user_id, NOW(), :user_id)
            ON CONFLICT (instance_id, nested_function_id, input_contextual_id) 
            DO UPDATE SET 
                input_value = EXCLUDED.input_value, 
                updated_at = NOW(), 
                updated_by = EXCLUDED.updated_by
            """
            
            self.db.execute(text(upsert_query), {
                "instance_id": instance_id,
                "nested_function_id": nested_function_id,
                "input_contextual_id": slot_id,
                "input_value": json_encoded_value,
                "user_id": user_id
            })
            
            self.logger.info(f"🔍 BUSINESS RULES: Populated input {nested_function_id}.{slot_id} = {input_value}")
            
        except Exception as e:
            self.logger.error(f"Error upserting nested function input: {str(e)}")
    
    def _execute_nested_function(self, nested_function_id: str, instance_id: str) -> Any:
        """Execute nested function using V2 adapter"""
        try:
            # Get function definition
            query = """
            SELECT function_name, function_type, parameters 
            FROM workflow_runtime.lo_nested_functions 
            WHERE nested_function_id = :nested_function_id
            """
            
            result = self.db.execute(text(query), {"nested_function_id": nested_function_id}).fetchone()
            
            if not result:
                self.logger.error(f"Nested function {nested_function_id} not found")
                return None
            
            function_name = result.function_name
            self.logger.info(f"🔍 BUSINESS RULES: Executing function: {function_name}")
            
            # Get nested function input parameters
            params_query = """
            SELECT item_id, slot_id, entity_id, attribute_id, value, data_type
            FROM workflow_runtime.lo_nested_function_input_items
            WHERE nested_function_id = :nested_function_id
            ORDER BY item_id
            """
            
            param_results = self.db.execute(text(params_query), {"nested_function_id": nested_function_id}).fetchall()
            
            # Build parameters for function execution
            execution_params = {}
            
            for param_row in param_results:
                slot_id = param_row.slot_id
                
                if slot_id:
                    # Get value from nested function input execution table
                    input_value_query = """
                    SELECT input_value FROM workflow_runtime.lo_nested_function_input_execution
                    WHERE instance_id = :instance_id 
                    AND nested_function_id = :nested_function_id 
                    AND input_contextual_id = :slot_id
                    ORDER BY created_at DESC LIMIT 1
                    """
                    
                    input_result = self.db.execute(text(input_value_query), {
                        "instance_id": instance_id,
                        "nested_function_id": nested_function_id,
                        "slot_id": slot_id
                    }).fetchone()
                    
                    if input_result and input_result.input_value:
                        import json
                        try:
                            param_value = json.loads(input_result.input_value)
                        except (json.JSONDecodeError, TypeError):
                            param_value = input_result.input_value
                        
                        execution_params[slot_id] = param_value
                        self.logger.info(f"🔍 BUSINESS RULES: Parameter {slot_id} = {param_value}")
            
            self.logger.info(f"🔍 BUSINESS RULES: Final execution parameters: {execution_params}")
            
            # Execute using V2 adapter
            from app.services.v2.v2_adapter import execute_nested_function_v2
            
            result_value = execute_nested_function_v2(
                function_name=function_name,
                db_session=self.db,
                input_values=execution_params,
                nested_function_id=nested_function_id
            )
            
            self.logger.info(f"🔍 BUSINESS RULES: Function {function_name} result: {result_value}")
            return result_value
            
        except Exception as e:
            self.logger.error(f"Error executing nested function {nested_function_id}: {str(e)}")
            return None
    
    def _update_validation_result(self, instance_id: str, validation_result: str):
        """Update the validationResult field in the entity table"""
        try:
            # Update the LeaveApplication entity's validation_result field
            update_query = """
            UPDATE workflow_runtime.e1_leaveapplication 
            SET validation_result = :validation_result
            WHERE leaveid IN (
                SELECT input_value::text 
                FROM workflow_runtime.lo_input_execution 
                WHERE instance_id = :instance_id 
                AND input_contextual_id LIKE '%leaveId%'
                LIMIT 1
            )
            """
            
            rows_updated = self.db.execute(text(update_query), {
                "instance_id": instance_id,
                "validation_result": validation_result
            }).rowcount
            
            self.logger.info(f"🔍 BUSINESS RULES: Updated validation_result to {validation_result} ({rows_updated} rows)")
            
        except Exception as e:
            self.logger.error(f"Error updating validation result: {str(e)}")
