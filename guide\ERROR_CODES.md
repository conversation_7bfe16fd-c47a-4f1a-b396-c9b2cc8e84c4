# API v2 Error Codes Documentation

## Overview

This document provides a comprehensive list of error codes returned by the v2 API endpoints. These error codes are designed to help frontend developers implement proper error handling and user feedback.

## Error Response Format

All error responses follow this standardized format:

```json
{
  "detail": {
    "error": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "field_name",
      "code": "SPECIFIC_ERROR_CODE",
      "additional_info": "value"
    }
  }
}
```

## HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 422 | Unprocessable Entity |
| 500 | Internal Server Error |

## Authentication Error Codes

### Registration Endpoint: `POST /api/v2/auth/register`

#### Success Response (201)
```json
{
  "user_id": "U5",
  "username": "testuser",
  "email": "<EMAIL>",
  "first_name": "Test",
  "last_name": "User",
  "status": "active",
  "roles": ["User"],
  "tenant_id": "t001",
  "disabled": false,
  "created_at": "2025-05-24T15:25:18.299616"
}
```

#### Error Responses

**409 - CONFLICT: User Already Exists**
```json
{
  "detail": {
    "error": "CONFLICT",
    "message": "User with this username or email already exists",
    "details": {
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

**422 - VALIDATION_ERROR: Invalid Input Data**
```json
{
  "detail": {
    "error": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "code": "INVALID_EMAIL_FORMAT"
    }
  }
}
```

**422 - VALIDATION_ERROR: Missing Required Field**
```json
{
  "detail": {
    "error": "VALIDATION_ERROR",
    "message": "Field required",
    "details": {
      "field": "username",
      "code": "FIELD_REQUIRED"
    }
  }
}
```

**422 - VALIDATION_ERROR: Field Length Validation**
```json
{
  "detail": {
    "error": "VALIDATION_ERROR",
    "message": "String should have at least 3 characters",
    "details": {
      "field": "username",
      "code": "STRING_TOO_SHORT"
    }
  }
}
```

**422 - VALIDATION_ERROR: Password Length**
```json
{
  "detail": {
    "error": "VALIDATION_ERROR",
    "message": "String should have at least 8 characters",
    "details": {
      "field": "password",
      "code": "PASSWORD_TOO_SHORT"
    }
  }
}
```

**500 - INTERNAL_ERROR: Server Error**
```json
{
  "detail": {
    "error": "INTERNAL_ERROR",
    "message": "Failed to create user account",
    "details": {}
  }
}
```

### Login Endpoint: `POST /api/v2/auth/login`

#### Success Response (200)
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_at": **********,
  "user": {
    "user_id": "U5",
    "username": "testuser",
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User",
    "status": "active",
    "roles": ["User"],
    "tenant_id": "t001",
    "disabled": false
  }
}
```

#### Error Responses

**401 - AUTHENTICATION_FAILED: Invalid Credentials**
```json
{
  "detail": {
    "error": "AUTHENTICATION_FAILED",
    "message": "Invalid username or password",
    "details": {}
  }
}
```

**422 - VALIDATION_ERROR: Missing Username**
```json
{
  "detail": {
    "error": "VALIDATION_ERROR",
    "message": "Field required",
    "details": {
      "field": "username",
      "code": "FIELD_REQUIRED"
    }
  }
}
```

**422 - VALIDATION_ERROR: Missing Password**
```json
{
  "detail": {
    "error": "VALIDATION_ERROR",
    "message": "Field required",
    "details": {
      "field": "password",
      "code": "FIELD_REQUIRED"
    }
  }
}
```

**500 - INTERNAL_ERROR: Authentication Error**
```json
{
  "detail": {
    "error": "INTERNAL_ERROR",
    "message": "An unexpected error occurred during authentication",
    "details": {}
  }
}
```

### Get User Endpoint: `GET /api/v2/auth/user/{user_id}`

#### Success Response (200)
```json
{
  "user_id": "U5",
  "username": "testuser",
  "email": "<EMAIL>",
  "first_name": "Test",
  "last_name": "User",
  "status": "active",
  "roles": ["User"],
  "tenant_id": "t001",
  "disabled": false,
  "created_at": "2025-05-24T15:25:18.069803"
}
```

#### Error Responses

**404 - NOT_FOUND: User Not Found**
```json
{
  "detail": {
    "error": "NOT_FOUND",
    "message": "User not found",
    "details": {
      "user_id": "U999"
    }
  }
}
```

**500 - INTERNAL_ERROR: Server Error**
```json
{
  "detail": {
    "error": "INTERNAL_ERROR",
    "message": "An error occurred while retrieving user information",
    "details": {}
  }
}
```

## Complete Error Code Reference

### Primary Error Codes

| Error Code | HTTP Status | Description | Endpoint |
|------------|-------------|-------------|----------|
| `CONFLICT` | 409 | Resource already exists | Register |
| `AUTHENTICATION_FAILED` | 401 | Invalid credentials | Login |
| `VALIDATION_ERROR` | 422 | Input validation failed | All |
| `NOT_FOUND` | 404 | Resource not found | Get User |
| `INTERNAL_ERROR` | 500 | Server error | All |

### Validation Error Sub-Codes

| Sub-Code | Description | Field Examples |
|----------|-------------|----------------|
| `FIELD_REQUIRED` | Required field is missing | username, email, password |
| `INVALID_EMAIL_FORMAT` | Email format is invalid | email |
| `STRING_TOO_SHORT` | String below minimum length | username (min 3) |
| `STRING_TOO_LONG` | String exceeds maximum length | first_name, last_name (max 100) |
| `PASSWORD_TOO_SHORT` | Password below minimum length | password (min 8) |
| `DUPLICATE_VALUE` | Value already exists | username, email |

## Frontend Implementation Guide

### Error Handling Strategy

1. **Check HTTP Status Code First**
   ```javascript
   if (response.status === 409) {
     // Handle conflict (user already exists)
   } else if (response.status === 401) {
     // Handle authentication failure
   } else if (response.status === 422) {
     // Handle validation errors
   }
   ```

2. **Parse Error Details**
   ```javascript
   const errorData = await response.json();
   const errorCode = errorData.detail.error;
   const message = errorData.detail.message;
   const details = errorData.detail.details;
   ```

3. **Field-Specific Error Handling**
   ```javascript
   if (details.field === 'email' && details.code === 'INVALID_EMAIL_FORMAT') {
     // Show email format error
   } else if (details.field === 'username' && details.code === 'DUPLICATE_VALUE') {
     // Show username taken error
   }
   ```

### Example Error Handling Implementation

```javascript
async function registerUser(userData) {
  try {
    const response = await fetch('/api/v2/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (response.ok) {
      const user = await response.json();
      return { success: true, user };
    }

    const errorData = await response.json();
    const { error, message, details } = errorData.detail;

    switch (error) {
      case 'CONFLICT':
        return { 
          success: false, 
          error: 'USER_EXISTS', 
          message: 'Username or email already taken' 
        };
      
      case 'VALIDATION_ERROR':
        return { 
          success: false, 
          error: 'VALIDATION', 
          field: details.field,
          message: message 
        };
      
      case 'INTERNAL_ERROR':
        return { 
          success: false, 
          error: 'SERVER_ERROR', 
          message: 'Please try again later' 
        };
      
      default:
        return { 
          success: false, 
          error: 'UNKNOWN', 
          message: message 
        };
    }
  } catch (error) {
    return { 
      success: false, 
      error: 'NETWORK_ERROR', 
      message: 'Network error occurred' 
    };
  }
}
```

### User-Friendly Error Messages

Map technical error codes to user-friendly messages:

```javascript
const ERROR_MESSAGES = {
  'CONFLICT': 'An account with this username or email already exists.',
  'AUTHENTICATION_FAILED': 'Invalid username or password. Please try again.',
  'FIELD_REQUIRED': 'This field is required.',
  'INVALID_EMAIL_FORMAT': 'Please enter a valid email address.',
  'STRING_TOO_SHORT': 'This field is too short.',
  'PASSWORD_TOO_SHORT': 'Password must be at least 8 characters long.',
  'NOT_FOUND': 'User not found.',
  'INTERNAL_ERROR': 'Something went wrong. Please try again later.',
};
```

## Testing Error Scenarios

### Registration Errors
```bash
# Test duplicate user
curl -X POST "http://localhost:8000/api/v2/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"username": "existing_user", "email": "<EMAIL>", "password": "password123"}'

# Test validation error
curl -X POST "http://localhost:8000/api/v2/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"username": "ab", "email": "invalid-email", "password": "123"}'
```

### Login Errors
```bash
# Test invalid credentials
curl -X POST "http://localhost:8000/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "nonexistent", "password": "wrongpassword"}'

# Test missing fields
curl -X POST "http://localhost:8000/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d '{}'
```

This comprehensive error code documentation should help your frontend team implement robust error handling for the v2 authentication API.
