"""
Format Enum Value Function

This function formats enum values based on predefined mappings or rules.
"""

from typing import Dict, Any, Optional
from app.services.v2.base_function import BaseSystemFunction
from app.services.v2.models import SystemFunctionInput, FunctionExecutionContext, FunctionCategory


class FormatEnumValueFunction(BaseSystemFunction):
    """Function to format enum values"""
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.TRANSFORM
    
    def get_required_inputs(self) -> list[str]:
        return ["value"]  # The value to format
    
    def get_optional_inputs(self) -> list[str]:
        return [
            "format_type",      # Type of formatting (default, uppercase, lowercase, title)
            "mapping",          # Custom mapping dict
            "default_value"     # Default value if formatting fails
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Any:
        """
        Execute format enum value function
        
        Args:
            input_data: Standardized system function input
            context: Function execution context
        
        Returns:
            Formatted enum value
        """
        self.log_info("🔧 Starting format_enum_value execution", context)
        
        # Get parameters from input values
        value = self.get_input_value(input_data, 'value')
        format_type = self.get_input_value(input_data, 'format_type', 'default')
        mapping = self.get_input_value(input_data, 'mapping', {})
        default_value = self.get_input_value(input_data, 'default_value', value)
        
        self.log_info(f"📋 Parameters: value={value}, format_type={format_type}, mapping={mapping}", context)
        
        if value is None:
            self.log_info(f"✅ Value is None, returning default: {default_value}", context)
            return default_value
        
        # Convert value to string for processing
        str_value = str(value)
        
        # Apply custom mapping first if provided
        if isinstance(mapping, dict) and str_value in mapping:
            formatted_value = mapping[str_value]
            self.log_info(f"✅ Applied custom mapping: {str_value} -> {formatted_value}", context)
            return formatted_value
        
        # Apply format type
        try:
            if format_type.lower() == 'uppercase':
                formatted_value = str_value.upper()
            elif format_type.lower() == 'lowercase':
                formatted_value = str_value.lower()
            elif format_type.lower() == 'title':
                formatted_value = str_value.title()
            elif format_type.lower() == 'capitalize':
                formatted_value = str_value.capitalize()
            else:
                # Default - return as-is
                formatted_value = str_value
            
            self.log_info(f"✅ Formatted enum value: {value} -> {formatted_value}", context)
            return formatted_value
            
        except Exception as e:
            self.log_error(f"Error formatting enum value: {str(e)}", context)
            return default_value
