# RBAC Integration Plan for Workflow System

This document outlines the detailed plan for integrating the Role-Based Access Control (RBAC) implementation into the Workflow System.

## Overview

The RBAC implementation provides authentication and permission services that need to be integrated into the workflow engine. The integration will enable secure access to workflows, entities, and functions based on user roles and permissions.

## Integration Components

1. **Main Application Integration**
2. **API Router Integration**
3. **Execution Engine Integration**
4. **Parameter Resolver Integration**
5. **API Endpoints Integration**
6. **Entity Service Enhancements**
7. **Migration Scripts**
8. **End-to-End Testing**

## 1. Main Application Integration

Update the main application (`main.py`) to include the authentication middleware.

### Tasks:

- Import the AuthMiddleware from the auth module
- Add the AuthMiddleware to the FastAPI application
- Configure paths to exclude from authentication (e.g., `/docs`, `/auth/token`)
- Update the error handler to handle authentication and permission exceptions

```python
# Import authentication middleware
from app.auth.auth_middleware import AuthMiddleware

# Add authentication middleware
app.add_middleware(
    AuthMiddleware,
    exclude_paths=[
        "/docs", 
        "/redoc", 
        "/openapi.json", 
        "/auth/token", 
        "/auth/refresh",
        "/health"
    ]
)
```

## 2. API Router Integration

Update the API router (`api.py`) to include the authentication and permission routes.

### Tasks:

- Import the authentication and permission routers
- Include the routers in the API router with appropriate prefixes

```python
# Import authentication and permission routers
from app.auth.auth_routes import router as auth_router
from app.auth.permission.permission_routes import router as permission_router

# Include authentication and permission routers
api_router.include_router(auth_router, prefix="/auth", tags=["authentication"])
api_router.include_router(permission_router, prefix="/permissions", tags=["permissions"])
```

## 3. Execution Engine Integration

Update the workflow execution engine (`execution_engine.py`) to support security context and permission checks.

### Tasks:

- Modify the WorkflowExecutionEngine class to accept a security context parameter
- Update the constructor to store the security context
- Implement permission checking in the _execute_local_objective method
- Update the _check_rbac method to use the permission service

```python
# Import security context and permission service
from app.auth.auth_middleware import SecurityContext
from app.auth.permission.permission_service import PermissionService, PermissionContext, ResourceType, PermissionType

class WorkflowExecutionEngine:
    def __init__(self, global_objective: GlobalObjective, security_context: SecurityContext = None, db: Session = None):
        """
        Initialize the workflow execution engine with a specific Global Objective.

        Args:
            global_objective (GlobalObjective): The Global Objective to be executed
            security_context (SecurityContext): Security context with user information
            db (Session, optional): SQLAlchemy database session
        """
        self.global_objective = global_objective
        self.security_context = security_context
        self.logger = logging.getLogger(self.__class__.__name__)
        self.execution_state: Dict[str, Any] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.db = db
        self.claude_service = ClaudeService()
        
    def _check_rbac(self, lo: LocalObjective) -> bool:
        """
        Check if the user has permission to execute a Local Objective.

        Args:
            lo (LocalObjective): Local Objective to check permission for

        Returns:
            bool: True if access is granted, False otherwise
        """
        # If no security context, deny access
        if not self.security_context or not self.security_context.authenticated:
            self.logger.warning("No security context or user not authenticated")
            return False
            
        # If user has Administrator role, grant access
        if "Administrator" in self.security_context.roles:
            self.logger.info("User has Administrator role, granting access")
            return True
            
        # Check permission using permission service
        try:
            # Create permission service
            permission_service = PermissionService(self.db)
            
            # Create permission context
            permission_context = PermissionContext(
                resource_type=ResourceType.FUNCTION,
                resource_id=lo.id,
                permission_type=PermissionType.EXECUTE,
                tenant_id=self.security_context.tenant_id
            )
            
            # Check permission
            has_permission = permission_service.check_permission(
                self.security_context, 
                permission_context
            )
            
            if has_permission:
                self.logger.info(f"✅ ACCESS GRANTED for user {self.security_context.username} to LO {lo.id}")
            else:
                self.logger.warning(f"❌ ACCESS DENIED for user {self.security_context.username} to LO {lo.id}")
                
            return has_permission
            
        except Exception as e:
            self.logger.error(f"Error checking RBAC: {str(e)}")
            return False
```

## 4. Parameter Resolver Integration

Update the parameter resolver (`parameter_resolver.py`) to support security context.

### Tasks:

- Modify the ParameterResolver class to accept a security context parameter
- Update the resolve_parameters method to include security context in the context data
- Add support for resolving security context variables in templates

```python
class ParameterResolver:
    def __init__(self, db_session=None, security_context=None):
        self.db_session = db_session
        self.security_context = security_context
        self._cache = {}
        self.resolution_depth = 0
        self.MAX_RESOLUTION_DEPTH = 10
        
    def resolve_parameters(self, raw_params: Dict[str, Any], context_data: Dict[str, Any], 
                          exclude_types: Set[str] = None) -> Dict[str, Any]:
        """
        Resolve all parameters with a unified strategy.
        
        Args:
            raw_params: The raw parameters from function definition
            context_data: The execution context with all available variables
            exclude_types: Set of input types to exclude from resolution (e.g., 'information')
            
        Returns:
            Resolved parameters with all references replaced by actual values
        """
        self.resolution_depth = 0  # Reset recursion counter
        
        if not raw_params:
            return {}
            
        # Deep copy to avoid modifying the original
        resolved_params = copy.deepcopy(raw_params)
        
        # Add security context to context data if available
        if self.security_context:
            security_data = {
                "user_id": self.security_context.user_id,
                "username": self.security_context.username,
                "roles": self.security_context.roles,
                "permissions": self.security_context.permissions,
                "org_units": self.security_context.org_units,
                "tenant_id": self.security_context.tenant_id
            }
            
            # Add security context to context data
            context_data.update({
                "security_context": security_data,
                "user_id": self.security_context.user_id,
                "username": self.security_context.username,
                "tenant_id": self.security_context.tenant_id
            })
        
        # Process each parameter recursively
        for key, value in resolved_params.items():
            resolved_params[key] = self._resolve_value(value, context_data, exclude_types)
            
        return resolved_params
```

## 5. API Endpoints Integration

Update the API endpoints to include authentication and permission checks.

### Tasks:

- Update the workflows.py endpoints to require authentication
- Add permission checks to sensitive endpoints
- Update other endpoint files with similar changes

Example for the workflows.py file:

```python
from app.auth.auth_middleware import get_security_context, require_auth, SecurityContext
from app.auth.permission.permission_service import PermissionService, PermissionContext, ResourceType, PermissionType

@router.post("/instances", response_model=WorkflowInstance)
async def create_workflow_instance(
    go_id: str = Body(..., description="Global Objective ID"),
    tenant_id: str = Body(..., description="Tenant ID"),
    test_mode: bool = Body(False, description="Whether this is a test instance"),
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Create a new workflow instance for a given global objective.
    """
    try:
        # Check permission
        permission_service = PermissionService(db)
        permission_context = PermissionContext(
            resource_type=ResourceType.FUNCTION,
            resource_id=go_id,
            permission_type=PermissionType.EXECUTE,
            tenant_id=tenant_id
        )
        
        # Require permission (will raise exception if not granted)
        permission_service.require_permission(security_context, permission_context)
        
        # Continue with existing implementation
        instance_id = str(uuid.uuid4())
        current_time = datetime.utcnow()
        
        # Use security context for user_id
        user_id = security_context.user_id
        
        # Rest of the implementation...
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating workflow instance: {str(e)}")
```

## 6. Entity Service Enhancements

Implement user/role as entities and relationship management, ensuring they can be used like any other entity in the workflow engine.

### Tasks:

- Create entity definitions for users and roles in the workflow engine
- Implement relationship management between users, roles, and permissions
- Update entity service to support user and role entities
- Ensure users and roles can be accessed in the input stack like other entities
- Enable CRUD operations on user and role entities through the workflow engine
- Implement session management for user authentication in a microservices architecture

### Implementation Details:

#### User and Role as Workflow Entities

Users and roles should be treated as first-class entities in the workflow engine, similar to other entities:

```python
# Example entity definition for User
user_entity = {
    "entity_id": "user",
    "display_name": "User",
    "attributes": [
        {"attribute_id": "user_id", "display_name": "User ID", "datatype": "string", "is_primary": True},
        {"attribute_id": "username", "display_name": "Username", "datatype": "string"},
        {"attribute_id": "email", "display_name": "Email", "datatype": "string"},
        {"attribute_id": "first_name", "display_name": "First Name", "datatype": "string"},
        {"attribute_id": "last_name", "display_name": "Last Name", "datatype": "string"},
        {"attribute_id": "status", "display_name": "Status", "datatype": "string"},
        {"attribute_id": "roles", "display_name": "Roles", "datatype": "array"}
    ]
}

# Example entity definition for Role
role_entity = {
    "entity_id": "role",
    "display_name": "Role",
    "attributes": [
        {"attribute_id": "role_id", "display_name": "Role ID", "datatype": "string", "is_primary": True},
        {"attribute_id": "name", "display_name": "Name", "datatype": "string"},
        {"attribute_id": "description", "display_name": "Description", "datatype": "string"},
        {"attribute_id": "permissions", "display_name": "Permissions", "datatype": "array"}
    ]
}
```

#### Input Stack Integration

Ensure that user and role entities can be accessed in the input stack:

```python
# Example of accessing user entity in parameter resolver
def resolve_user_entity(user_id: str, db: Session) -> Dict[str, Any]:
    """
    Resolve user entity from database.
    
    Args:
        user_id: User ID to resolve
        db: Database session
        
    Returns:
        Dict[str, Any]: User entity data
    """
    query = """
    SELECT 
        u.user_id, u.username, u.email, u.first_name, u.last_name, u.status,
        array_agg(DISTINCT ur.role) as roles
    FROM workflow_runtime.users u
    LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
    WHERE u.user_id = :user_id
    GROUP BY u.user_id, u.username, u.email, u.first_name, u.last_name, u.status
    """
    
    result = db.execute(text(query), {"user_id": user_id}).fetchone()
    
    if not result:
        return None
        
    return {
        "user_id": result.user_id,
        "username": result.username,
        "email": result.email,
        "first_name": result.first_name,
        "last_name": result.last_name,
        "status": result.status,
        "roles": result.roles if result.roles and result.roles[0] is not None else []
    }
```

#### Session Management

Implement session management for user authentication in a microservices architecture:

```python
# Example of session management in a microservices architecture
def validate_session(session_id: str, db: Session) -> Optional[Dict[str, Any]]:
    """
    Validate a session and return the associated user.
    
    Args:
        session_id: Session ID to validate
        db: Database session
        
    Returns:
        Optional[Dict[str, Any]]: User data if session is valid, None otherwise
    """
    query = """
    SELECT 
        s.session_id, s.user_id, s.expires_at,
        u.username, u.email, u.first_name, u.last_name, u.status,
        array_agg(DISTINCT ur.role) as roles
    FROM workflow_runtime.user_sessions s
    JOIN workflow_runtime.users u ON s.user_id = u.user_id
    LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
    WHERE s.session_id = :session_id AND s.expires_at > CURRENT_TIMESTAMP
    GROUP BY s.session_id, s.user_id, s.expires_at, u.username, u.email, u.first_name, u.last_name, u.status
    """
    
    result = db.execute(text(query), {"session_id": session_id}).fetchone()
    
    if not result:
        return None
        
    return {
        "session_id": result.session_id,
        "user_id": result.user_id,
        "username": result.username,
        "email": result.email,
        "first_name": result.first_name,
        "last_name": result.last_name,
        "status": result.status,
        "roles": result.roles if result.roles and result.roles[0] is not None else [],
        "expires_at": result.expires_at
    }
```

## 7. Migration Scripts

Create scripts to apply database changes to the production environment.

### Tasks:

- Create SQL scripts for database schema updates
- Create data migration scripts for existing users and roles
- Create scripts to set up initial permissions and roles

Example migration script:

```sql
-- Create RBAC tables if they don't exist
CREATE TABLE IF NOT EXISTS workflow_runtime.users (
    user_id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS workflow_runtime.roles (
    role_id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add more tables as needed...
```

## 8. End-to-End Testing

Test the complete implementation in a production-like environment.

### Tasks:

- Create test cases for authentication and permission checks
- Test workflow execution with different user roles
- Test API endpoints with authentication and permission checks
- Verify that all components work together correctly

## Implementation Sequence

1. **Database Schema Updates**
   - Apply database schema updates to add RBAC tables
   - Migrate existing user data if needed

2. **Main Application and API Router Integration**
   - Update main.py to add AuthMiddleware
   - Update api.py to include auth and permission routers

3. **Core Services Integration**
   - Update execution_engine.py to support security context
   - Update parameter_resolver.py to support security context

4. **API Endpoints Integration**
   - Update workflows.py and other endpoint files with auth and permission checks

5. **Entity Service Enhancements**
   - Implement user/role as entities
   - Implement relationship management

6. **Testing and Validation**
   - Test each component individually
   - Perform end-to-end testing
   - Validate the implementation against requirements

## Conclusion

This integration plan provides a comprehensive approach to integrating the RBAC implementation into the Workflow System. By following this plan, we can ensure that all components work together correctly and that the system is secure and properly authenticated.
