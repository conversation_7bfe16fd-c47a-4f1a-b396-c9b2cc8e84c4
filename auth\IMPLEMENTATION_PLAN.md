# Implementation Plan for Fixing Duplicate Input IDs Issue

## Problem Summary

We've identified an issue with duplicate input IDs in the workflow system. The problem occurs when the insert generator creates input items with the same ID (e.g., "in003") for different local objectives. This causes confusion in the system when it tries to resolve parameter references in nested functions.

## Investigation Findings

1. **Duplicate Input IDs**: We found multiple entries with the same ID (e.g., "in003") in the `lo_input_items` table, but with different `input_stack_id` values.

2. **Parameter Resolution Issue**: When executing a workflow, the system tries to resolve template strings like `${in003}` to actual values, but it can't find the correct input because of the duplicate IDs.

3. **Display Name Mismatch**: After fixing the duplicate IDs by renaming them (e.g., "in003" to "in003_2"), we found that the display names in the database don't match what we're passing in the API request. For example, we're passing "Start Date" but the database has "Email" for the same input ID.

4. **Database Inconsistency**: The database appears to be in an inconsistent state, with input IDs, display names, and nested function parameters not properly aligned.

## Solution Plan

1. **Fix Insert Generator**: The root cause is in the insert generator that doesn't properly increment input IDs. We need to modify it to ensure unique IDs are generated for each input item.

2. **Clean Database**: Before applying the fix, we should clean up the database to remove all inconsistent data.

3. **Re-run YAML Import**: After cleaning the database, we should re-run the YAML import process to create a fresh, consistent set of data.

4. **Verify API Sequence**: Finally, we should verify the API sequence to ensure that all endpoints work correctly with the new data.

## Implementation Steps

1. **Modify Insert Generator**:
   - Update the insert generator to check for existing IDs before creating new ones
   - Ensure IDs are incremented properly for each input item

2. **Create Database Cleanup Script**:
   - Create a script to remove all workflow-related data from the database
   - Ensure all tables are reset to a clean state

3. **Re-import YAML Data**:
   - Run the YAML import process with the fixed insert generator
   - Verify that all data is imported correctly

4. **Test API Sequence**:
   - Test the complete API sequence from user registration to workflow execution
   - Verify that all endpoints work correctly with the new data

## Next Steps

1. Implement the fix for the insert generator
2. Clean up the database
3. Re-import the YAML data
4. Test the API sequence
