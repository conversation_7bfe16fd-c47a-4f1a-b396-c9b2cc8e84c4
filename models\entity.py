from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class EntityType(str, Enum):
    REGULAR = "Regular"
    NESTED = "Nested"
    SHARED = "Shared"
    JOINT = "Joint"

class EntityStatus(str, Enum):
    DRAFT = "Draft"
    ACTIVE = "Active"
    INACTIVE = "Inactive"
    ARCHIVED = "Archived"

class EntityAttribute(BaseModel):
    id: str
    name: str
    datatype: str
    required: bool = False
    auto_generated: bool = False
    source_entity: Optional[str] = None
    source_attribute: Optional[str] = None
    default_value: Optional[Any] = None
    pattern: Optional[str] = None
    precision: Optional[int] = None
    values: Optional[List[Any]] = None
    validations: Optional[List[Dict[str, Any]]] = None
    relationships: Optional[List[Dict[str, Any]]] = None
    status: str = "Deployed"
    version: str

class Entity(BaseModel):
    tenant_id: str
    entity_id: str
    name: str
    type: EntityType = EntityType.REGULAR
    parent_entity: Optional[str] = None
    shared_with_entities: Optional[List[str]] = None
    joint_entities: Optional[List[str]] = None
    status: EntityStatus = EntityStatus.ACTIVE
    description: Optional[str] = None
    attributes: List[EntityAttribute] = []
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: str
