# Schema Mapping Analysis: workflow_temp vs workflow_runtime

## Executive Summary

This document provides a comprehensive table-by-table, column-by-column analysis of the differences between the `workflow_temp` and `workflow_runtime` schemas. The analysis identifies new tables, modified columns, and structural changes that need to be ported to the runtime schema along with their data.

## Key Findings

### New Tables in workflow_temp (Not in workflow_runtime)
1. **advanced_process_intelligence** - Process intelligence and analytics
2. **attribute_constraints** - Enhanced attribute validation constraints
3. **attribute_metadata** - Extended attribute metadata
4. **bottleneck_analysis** - Performance bottleneck analysis
5. **business_rule_conditions** - Business rule condition definitions
6. **business_rules** - Business rule definitions
7. **conformance_analytics** - Process conformance analytics
8. **critical_lo_performance** - Critical local objective performance metrics
9. **data_constraints** - Data validation constraints
10. **exception_patterns** - Exception pattern analysis
11. **execution_variance** - Execution variance analytics
12. **go_validation_rule_details** - Global objective validation rule details
13. **go_validation_rules** - Global objective validation rules
14. **lo_attribute_mappings** - Local objective attribute mappings
15. **lo_entity_mappings** - Local objective entity mappings
16. **lo_entity_validations** - Local objective entity validations
17. **lo_execution_data** - Local objective execution data
18. **lo_input_mappings** - Local objective input mappings
19. **lo_nested_function_input_items** - Nested function input items
20. **lo_nested_function_input_stacks** - Nested function input stacks
21. **lo_nested_function_mappings** - Nested function mappings
22. **lo_nested_function_output_items** - Nested function output items
23. **lo_nested_function_output_stacks** - Nested function output stacks
24. **lo_output_mappings** - Local objective output mappings
25. **lo_ui_entity_attribute_stack** - UI entity attribute stack
26. **lo_ui_items** - Local objective UI items
27. **lo_ui_stacks** - Local objective UI stacks
28. **local_objectives_list** - Local objectives list
29. **optimization_insights** - Process optimization insights
30. **output_item_metadata** - Output item metadata
31. **pathway_definitions** - Pathway definitions
32. **pathway_frequency** - Pathway frequency analysis
33. **performance_discovery_metrics** - Performance discovery metrics
34. **performance_metadata** - Performance metadata
35. **prediction_models** - Prediction models
36. **process_flow** - Process flow definitions
37. **process_flow_conditions** - Process flow conditions
38. **process_health_score** - Process health scoring
39. **process_mining_schema** - Process mining schema
40. **process_ownership** - Process ownership
41. **relationship_constraints** - Relationship constraints
42. **relationship_properties** - Relationship properties
43. **resource_patterns** - Resource pattern analysis
44. **role_decision_authority** - Role decision authority
45. **role_kpis** - Role KPIs
46. **role_metadata** - Role metadata
47. **rollback_analytics** - Rollback analytics
48. **rollback_pathways** - Rollback pathways
49. **rollback_pathways_metrics** - Rollback pathway metrics
50. **rollback_triggers** - Rollback triggers
51. **sla_thresholds** - SLA thresholds
52. **trigger_attributes** - Trigger attributes
53. **trigger_definition** - Trigger definitions
54. **volume_metrics** - Volume metrics

### Modified Tables (Existing in Both Schemas)

#### 1. **global_objectives**
**New Columns in workflow_temp:**
- `metadata` (jsonb) - Additional metadata storage
- `auto_id` (integer) - Auto-incrementing ID
- `primary_entity` (varchar) - Primary entity reference
- `classification` (varchar) - Classification type
- `tenant_name` (varchar) - Tenant name
- `book_id` (varchar) - Book reference ID
- `book_name` (varchar) - Book name
- `chapter_id` (varchar) - Chapter reference ID
- `chapter_name` (varchar) - Chapter name
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

**Modified Columns:**
- `process_mining_schema` and `performance_metadata` remain jsonb but may have different structure

#### 2. **local_objectives**
**New Columns in workflow_temp:**
- `auto_id` (integer) - Auto-incrementing ID
- `agent_type` (varchar) - Agent type classification
- `execution_rights` (varchar) - Execution rights
- `status` (varchar) - Status field
- `version` (varchar) - Version information
- `description` (varchar) - Description field
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

**Removed Columns:**
- `contextual_id` - No longer present in workflow_temp

#### 3. **entities**
**New Columns in workflow_temp:**
- `display_name` (varchar) - Display name for entities
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 4. **entity_attributes**
**New Columns in workflow_temp:**
- `display_name` (varchar) - Display name for attributes
- `is_primary_key` (boolean) - Primary key indicator
- `is_foreign_key` (boolean) - Foreign key indicator
- `is_unique` (boolean) - Unique constraint indicator
- `min_value` (varchar) - Minimum value constraint
- `max_value` (varchar) - Maximum value constraint
- `min_length` (integer) - Minimum length constraint
- `max_length` (integer) - Maximum length constraint
- `pattern` (varchar) - Pattern validation
- `is_calculated` (boolean) - Calculated field indicator
- `calculation_formula` (text) - Calculation formula
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 5. **entity_business_rules**
**Modified Structure:**
- `inputs` (jsonb) - Changed from text to jsonb
- `outputs` (jsonb) - Changed from text to jsonb
- `created_by` (varchar) - New field
- `updated_by` (varchar) - New field

#### 6. **entity_relationships**
**New Columns in workflow_temp:**
- `on_delete` (varchar) - Delete cascade behavior
- `on_update` (varchar) - Update cascade behavior
- `foreign_key_type` (varchar) - Foreign key type
- `description` (text) - Relationship description
- `version` (varchar) - Version information
- `status` (varchar) - Status field
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 7. **lo_input_items**
**New Columns in workflow_temp:**
- `read_only` (boolean) - Read-only indicator
- `agent_type` (varchar) - Agent type
- `dependent_attribute` (boolean) - Dependency indicator
- `dependent_attribute_value` (text) - Dependency values
- `enum_values` (jsonb) - Enumeration values
- `default_value` (text) - Default value
- `information_field` (boolean) - Information field indicator
- `constant_field` (boolean) - Constant field indicator
- `entity_id` (varchar) - Entity reference
- `attribute_id` (varchar) - Attribute reference
- `entity_name` (varchar) - Entity name
- `attribute_name` (varchar) - Attribute name

#### 8. **lo_input_stack**
**New Columns in workflow_temp:**
- `lo_input_stack_id` (varchar) - Stack identifier
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 9. **lo_output_items**
**New Columns in workflow_temp:**
- `nested_function_id` (varchar) - Nested function reference
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 10. **lo_output_stack**
**New Columns in workflow_temp:**
- `lo_output_stack_id` (varchar) - Stack identifier
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 11. **lo_data_mapping_stack**
**New Columns in workflow_temp:**
- `lo_data_mapping_stack_id` (varchar) - Stack identifier
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 12. **lo_data_mappings**
**New Columns in workflow_temp:**
- `auto_id` (integer) - Auto-incrementing ID
- `source_entity` (varchar) - Source entity name
- `source_attribute` (varchar) - Source attribute name
- `target_entity` (varchar) - Target entity name
- `target_attribute` (varchar) - Target attribute name
- `lo_data_mapping_item_id` (varchar) - Mapping item ID
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 13. **lo_nested_functions**
**New Columns in workflow_temp:**
- `description` (varchar) - Function description
- `returns` (varchar) - Return type
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 14. **execution_pathways**
**Modified Structure:**
- Changed from integer `id` to varchar `id`
- Added `execution_pathway_id` (varchar)
- Added `created_by` and `updated_by` fields

#### 15. **execution_pathway_conditions**
**New Columns in workflow_temp:**
- `execution_pathway_condition_id` (varchar) - Condition identifier
- `multiple_conditions` (varchar) - Multiple conditions support
- `multiple_conditions_operator` (varchar) - Multiple conditions operator
- `created_by` (varchar) - Creator user ID
- `updated_by` (varchar) - Last updater user ID

#### 16. **terminal_pathways**
**Modified Structure:**
- Changed primary key from `lo_id` to `id`
- Added `go_id` (varchar) - Global objective reference
- Added `pathway_data` (jsonb) - Pathway data
- Added `sequence_number` (integer) - Sequence number

#### 17. **ui_elements**
**New Columns in workflow_temp:**
- `entity_id` (varchar) - Entity reference
- `attribute_id` (varchar) - Attribute reference
- `read_only` (boolean) - Read-only indicator

#### 18. **data_mappings**
**New Columns in workflow_temp:**
- `auto_id` (integer) - Auto-incrementing ID
- `metadata` (jsonb) - Additional metadata

#### 19. **output_items**
**New Columns in workflow_temp:**
- `auto_id` (integer) - Auto-incrementing ID
- `metadata` (jsonb) - Additional metadata

#### 20. **output_stack**
**Modified Structure:**
- Changed `go_id` from NOT NULL to nullable
- Added `created_by` and `updated_by` fields

#### 21. **roles**
**New Columns in workflow_temp:**
- `id` (integer) - Auto-incrementing ID

#### 22. **users**
**New Columns in workflow_temp:**
- `id` (integer) - Auto-incrementing ID

#### 23. **user_roles**
**New Columns in workflow_temp:**
- `id` (integer) - Auto-incrementing ID

#### 24. **role_inheritance**
**New Columns in workflow_temp:**
- `id` (integer) - Auto-incrementing ID

#### 25. **attribute_validations**
**New Columns in workflow_temp:**
- `validation_type` (varchar) - Validation type
- `validation_rule` (text) - Validation rule
- `error_message` (text) - Error message
- `validation_timing` (varchar) - Validation timing
- `validation_id` (integer) - Validation ID
- `id` (integer) - Auto-incrementing ID
- `executable_rule` (text) - Executable rule

## Data Migration Requirements

### High Priority Tables (Core Functionality)
1. **global_objectives** - Add new metadata fields
2. **local_objectives** - Add agent_type, execution_rights, status fields
3. **entities** - Add display_name and audit fields
4. **entity_attributes** - Add validation and constraint fields
5. **lo_input_items** - Add dependency and UI control fields
6. **lo_nested_functions** - Add description and audit fields

### Medium Priority Tables (Enhanced Features)
1. **entity_business_rules** - Convert text fields to jsonb
2. **entity_relationships** - Add constraint and behavior fields
3. **execution_pathways** - Modify ID structure
4. **ui_elements** - Add entity/attribute references

### New Feature Tables (Process Intelligence)
1. **advanced_process_intelligence** and related analytics tables
2. **business_rules** and **business_rule_conditions**
3. **lo_ui_stacks** and **lo_ui_items** for enhanced UI
4. **performance_metadata** and related metrics tables

## Impact on APIs

### Affected Endpoints
1. **GET /global-objectives/** - Need to handle new metadata fields
2. **GET /instances/{instance_id}/inputs** - Enhanced with new input types and UI controls
3. **POST /instances/{instance_id}/execute** - Support for new nested function structure
4. **Entity management APIs** - Handle new attribute constraints and validations

### New API Requirements
1. **Business Rules Management** - CRUD operations for business rules
2. **Process Intelligence APIs** - Analytics and performance metrics
3. **UI Configuration APIs** - Dynamic UI generation based on lo_ui_stacks
4. **Advanced Validation APIs** - Complex validation rule management

## Recommendations

### Phase 1: Core Schema Migration
1. Migrate high-priority table changes
2. Update existing APIs to handle new fields
3. Maintain backward compatibility

### Phase 2: Enhanced Features
1. Implement business rules engine
2. Add process intelligence capabilities
3. Enhance UI generation system

### Phase 3: Advanced Analytics
1. Implement performance monitoring
2. Add predictive analytics
3. Complete process mining integration

## Next Steps
1. Create detailed migration scripts for each table
2. Update API endpoints to handle new schema
3. Implement data validation for new constraints
4. Create comprehensive testing strategy
