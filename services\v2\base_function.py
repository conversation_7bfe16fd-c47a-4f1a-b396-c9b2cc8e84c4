"""
V2 System Functions Base Interface

Base class and interface for all V2 system functions with standardized execution pattern.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging
import time
import uuid
from sqlalchemy.orm import Session

from .models import (
    SystemFunctionInput, 
    SystemFunctionOutput, 
    FunctionExecutionContext,
    ExecutionStatus,
    FunctionCategory
)

logger = logging.getLogger(__name__)

class BaseSystemFunction(ABC):
    """
    Abstract base class for all V2 system functions.
    Provides standardized execution pattern, logging, and error handling.
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
        self.function_name = self.__class__.__name__.lower().replace('function', '')
        self.category = self.get_category()
    
    @abstractmethod
    def get_category(self) -> FunctionCategory:
        """Return the category of this function"""
        pass
    
    @abstractmethod
    def get_required_inputs(self) -> list[str]:
        """Return list of required input field names"""
        pass
    
    @abstractmethod
    def get_optional_inputs(self) -> list[str]:
        """Return list of optional input field names"""
        pass
    
    @abstractmethod
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Any:
        """
        Execute the core function logic.
        This method should be implemented by each specific function.
        """
        pass
    
    def validate_inputs(self, input_data: SystemFunctionInput) -> tuple[bool, Optional[str]]:
        """
        Validate that all required inputs are present.
        Returns (is_valid, error_message)
        """
        required_inputs = self.get_required_inputs()
        missing_inputs = []
        
        for required_input in required_inputs:
            # Check in input_values first, then in function_params
            if (required_input not in input_data.input_values and 
                required_input not in input_data.function_params):
                missing_inputs.append(required_input)
        
        if missing_inputs:
            return False, f"Missing required inputs: {', '.join(missing_inputs)}"
        
        return True, None
    
    def get_input_value(self, input_data: SystemFunctionInput, key: str, default: Any = None) -> Any:
        """
        Get input value from either input_values or function_params.
        Prioritizes input_values over function_params.
        """
        if key in input_data.input_values:
            return input_data.input_values[key]
        elif key in input_data.function_params:
            return input_data.function_params[key]
        else:
            return default
    
    def execute(self, input_data: SystemFunctionInput) -> SystemFunctionOutput:
        """
        Main execution method with standardized error handling, logging, and timing.
        This method should not be overridden by specific functions.
        """
        execution_id = str(uuid.uuid4())
        start_time = time.time()
        
        # Create execution context
        context = FunctionExecutionContext(
            function_name=self.function_name,
            category=self.category,
            go_id=input_data.go_id,
            lo_id=input_data.lo_id,
            user_id=input_data.user_id,
            tenant_id=input_data.tenant_id,
            instance_id=input_data.instance_id,
            execution_id=execution_id
        )
        
        self.logger.info(f"🔧 EXECUTING V2 FUNCTION: {self.function_name}")
        self.logger.info(f"📋 Execution ID: {execution_id}")
        self.logger.info(f"📋 Context: GO={input_data.go_id}, LO={input_data.lo_id}")
        self.logger.info(f"📋 Input Values: {input_data.input_values}")
        self.logger.info(f"📋 Function Params: {input_data.function_params}")
        
        try:
            # Validate inputs
            is_valid, error_message = self.validate_inputs(input_data)
            if not is_valid:
                self.logger.error(f"❌ Input validation failed: {error_message}")
                return SystemFunctionOutput(
                    status=ExecutionStatus.ERROR,
                    function_name=self.function_name,
                    error_message=error_message,
                    error_code="INVALID_INPUT",
                    execution_time_ms=(time.time() - start_time) * 1000
                )
            
            # Execute the function
            self.logger.info(f"🚀 Starting function execution...")
            result = self.execute_function(input_data, context)
            
            execution_time = (time.time() - start_time) * 1000
            self.logger.info(f"✅ Function executed successfully in {execution_time:.2f}ms")
            self.logger.info(f"📤 Result: {result}")
            
            return SystemFunctionOutput(
                status=ExecutionStatus.SUCCESS,
                function_name=self.function_name,
                result=result,
                execution_time_ms=execution_time,
                metadata={
                    "execution_id": execution_id,
                    "category": self.category.value,
                    "context": context.dict()
                }
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            error_message = str(e)
            
            self.logger.error(f"💥 Function execution failed: {error_message}")
            self.logger.error(f"💥 Execution time: {execution_time:.2f}ms")
            
            return SystemFunctionOutput(
                status=ExecutionStatus.ERROR,
                function_name=self.function_name,
                error_message=error_message,
                error_code="EXECUTION_ERROR",
                execution_time_ms=execution_time,
                metadata={
                    "execution_id": execution_id,
                    "category": self.category.value,
                    "context": context.dict()
                }
            )
    
    def log_debug(self, message: str, context: Optional[FunctionExecutionContext] = None):
        """Enhanced debug logging with context"""
        if context:
            self.logger.debug(f"[{context.execution_id}] {self.function_name}: {message}")
        else:
            self.logger.debug(f"{self.function_name}: {message}")
    
    def log_info(self, message: str, context: Optional[FunctionExecutionContext] = None):
        """Enhanced info logging with context"""
        if context:
            self.logger.info(f"[{context.execution_id}] {self.function_name}: {message}")
        else:
            self.logger.info(f"{self.function_name}: {message}")
    
    def log_warning(self, message: str, context: Optional[FunctionExecutionContext] = None):
        """Enhanced warning logging with context"""
        if context:
            self.logger.warning(f"[{context.execution_id}] {self.function_name}: {message}")
        else:
            self.logger.warning(f"{self.function_name}: {message}")
    
    def log_error(self, message: str, context: Optional[FunctionExecutionContext] = None):
        """Enhanced error logging with context"""
        if context:
            self.logger.error(f"[{context.execution_id}] {self.function_name}: {message}")
        else:
            self.logger.error(f"{self.function_name}: {message}")
