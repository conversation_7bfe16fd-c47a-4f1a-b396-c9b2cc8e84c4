"""
Authentication Middleware for the Workflow System.

This module provides middleware for FastAPI to:
- Extract JWT tokens from requests
- Validate tokens
- Populate security context
"""

import logging
from typing import Optional, Dict, Any, List, Callable

from fastapi import Request, Response, HTTPException, status, Depends
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer
from sqlalchemy.orm import Session
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.auth.auth_service import AuthService
# Import the appropriate get_db function based on whether we're running tests
import os
import sys

# Check if we're running tests
if 'unittest' in sys.modules or 'pytest' in sys.modules or any('test' in arg for arg in sys.argv):
    # Use the test database session
    from app.auth.test_db import get_db
else:
    # Use the regular database session
    from app.db.session import get_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("auth_middleware")

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

class SecurityContext:
    """
    Security context for the current request.
    
    Contains user information, roles, permissions, and other security-related data.
    """
    
    def __init__(self):
        self.user_id: Optional[str] = None
        self.username: Optional[str] = None
        self.roles: List[str] = []
        self.permissions: List[str] = []
        self.org_units: List[str] = []
        self.tenant_id: Optional[str] = None
        self.authenticated: bool = False
        self.session_id: Optional[str] = None
        self.token: Optional[str] = None
        
    def has_role(self, role: str) -> bool:
        """
        Check if the user has a specific role.
        
        Args:
            role: Role to check
            
        Returns:
            bool: True if the user has the role, False otherwise
        """
        return role in self.roles
        
    def has_permission(self, permission: str) -> bool:
        """
        Check if the user has a specific permission.
        
        Args:
            permission: Permission to check
            
        Returns:
            bool: True if the user has the permission, False otherwise
        """
        return permission in self.permissions
        
    def has_any_permission(self, permissions: List[str]) -> bool:
        """
        Check if the user has any of the specified permissions.
        
        Args:
            permissions: List of permissions to check
            
        Returns:
            bool: True if the user has any of the permissions, False otherwise
        """
        return any(permission in self.permissions for permission in permissions)
        
    def has_all_permissions(self, permissions: List[str]) -> bool:
        """
        Check if the user has all of the specified permissions.
        
        Args:
            permissions: List of permissions to check
            
        Returns:
            bool: True if the user has all of the permissions, False otherwise
        """
        return all(permission in self.permissions for permission in permissions)
        
    def in_org_unit(self, org_unit: str) -> bool:
        """
        Check if the user is in a specific organizational unit.
        
        Args:
            org_unit: Organizational unit to check
            
        Returns:
            bool: True if the user is in the organizational unit, False otherwise
        """
        return org_unit in self.org_units
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the security context to a dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the security context
        """
        return {
            "user_id": self.user_id,
            "username": self.username,
            "roles": self.roles,
            "permissions": self.permissions,
            "org_units": self.org_units,
            "tenant_id": self.tenant_id,
            "authenticated": self.authenticated,
            "session_id": self.session_id
        }

class AuthMiddleware(BaseHTTPMiddleware):
    """
    Authentication middleware for FastAPI.
    
    Extracts JWT tokens from requests, validates them, and populates the security context.
    """
    
    def __init__(
        self, 
        app: ASGIApp, 
        exclude_paths: List[str] = None
    ):
        """
        Initialize the authentication middleware.
        
        Args:
            app: FastAPI application
            exclude_paths: List of paths to exclude from authentication
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/docs", "/redoc", "/openapi.json", "/token", "/refresh"]
        self.logger = logging.getLogger("auth_middleware")
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and add security context.
        
        Args:
            request: FastAPI request
            call_next: Next middleware in the chain
            
        Returns:
            Response: FastAPI response
        """
        # Skip authentication for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
            
        # Create security context
        security_context = SecurityContext()
        
        # Extract token from request
        token = self._extract_token(request)
        
        if token:
            try:
                # Get database session
                db = next(get_db())
                
                # Create auth service
                auth_service = AuthService(db)
                
                # Decode token
                payload = auth_service.decode_token(token)
                
                # Get session
                session = auth_service.get_session(token)
                
                if session:
                    # Populate security context
                    security_context.user_id = payload.get("sub")
                    security_context.username = payload.get("username")
                    security_context.roles = payload.get("roles", [])
                    security_context.permissions = payload.get("permissions", [])
                    security_context.org_units = payload.get("org_units", [])
                    security_context.tenant_id = payload.get("tenant_id")
                    security_context.authenticated = True
                    security_context.session_id = session.session_id
                    security_context.token = token
                    
                    # Add security context to request state
                    request.state.security_context = security_context
                    
                    self.logger.info(f"Authenticated user: {security_context.username}")
                else:
                    self.logger.warning("Invalid session")
                    
            except HTTPException as e:
                self.logger.warning(f"Authentication error: {e.detail}")
                
            except Exception as e:
                self.logger.error(f"Error processing token: {str(e)}")
                
        # Add security context to request state even if not authenticated
        if not hasattr(request.state, "security_context"):
            request.state.security_context = security_context
            
        # Process request
        return await call_next(request)
        
    def _extract_token(self, request: Request) -> Optional[str]:
        """
        Extract JWT token from request.
        
        Args:
            request: FastAPI request
            
        Returns:
            Optional[str]: JWT token if found, None otherwise
        """
        # Check Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header.replace("Bearer ", "")
            
        # Check cookie
        token = request.cookies.get("access_token")
        if token:
            return token
            
        # Check query parameter
        token = request.query_params.get("access_token")
        if token:
            return token
            
        return None

def get_security_context(request: Request) -> SecurityContext:
    """
    Get the security context from the request.
    
    Args:
        request: FastAPI request
        
    Returns:
        SecurityContext: Security context for the current request
    """
    return getattr(request.state, "security_context", SecurityContext())

def require_auth(request: Request) -> SecurityContext:
    """
    Require authentication for a route.
    
    Args:
        request: FastAPI request
        
    Returns:
        SecurityContext: Security context for the current request
        
    Raises:
        HTTPException: If the user is not authenticated
    """
    security_context = get_security_context(request)
    
    if not security_context.authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    return security_context

def require_role(role: str):
    """
    Require a specific role for a route.
    
    Args:
        role: Role to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: SecurityContext = Depends(require_auth)):
        if not security_context.has_role(role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role}' required",
            )
            
        return security_context
        
    return dependency

def require_permission(permission: str):
    """
    Require a specific permission for a route.
    
    Args:
        permission: Permission to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: SecurityContext = Depends(require_auth)):
        if not security_context.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required",
            )
            
        return security_context
        
    return dependency

def require_any_permission(permissions: List[str]):
    """
    Require any of the specified permissions for a route.
    
    Args:
        permissions: List of permissions to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: SecurityContext = Depends(require_auth)):
        if not security_context.has_any_permission(permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of permissions {permissions} required",
            )
            
        return security_context
        
    return dependency

def require_all_permissions(permissions: List[str]):
    """
    Require all of the specified permissions for a route.
    
    Args:
        permissions: List of permissions to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: SecurityContext = Depends(require_auth)):
        if not security_context.has_all_permissions(permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"All permissions {permissions} required",
            )
            
        return security_context
        
    return dependency

def require_org_unit(org_unit: str):
    """
    Require a specific organizational unit for a route.
    
    Args:
        org_unit: Organizational unit to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: SecurityContext = Depends(require_auth)):
        if not security_context.in_org_unit(org_unit):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Organizational unit '{org_unit}' required",
            )
            
        return security_context
        
    return dependency

def require_tenant(tenant_id: str):
    """
    Require a specific tenant for a route.
    
    Args:
        tenant_id: Tenant ID to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: SecurityContext = Depends(require_auth)):
        if security_context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Tenant '{tenant_id}' required",
            )
            
        return security_context
        
    return dependency
