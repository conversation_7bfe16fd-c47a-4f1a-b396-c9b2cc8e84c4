from typing import Dict, Any, List, Union, Optional
import re
import logging
import operator

logger = logging.getLogger(__name__)

class ConditionEvaluator:
    """
    Evaluates conditions in workflow decision points.
    Supports complex nested conditions with AND/OR logic.
    """
    
    # Mapping of operator strings to actual operator functions
    OPERATORS = {
        "==": operator.eq,
        "!=": operator.ne,
        ">": operator.gt,
        ">=": operator.ge,
        "<": operator.lt,
        "<=": operator.le,
        "in": lambda a, b: a in b,
        "not in": lambda a, b: a not in b,
        "contains": lambda a, b: b in a if hasattr(a, "__contains__") else False,
        "startswith": lambda a, b: a.startswith(b) if hasattr(a, "startswith") else False,
        "endswith": lambda a, b: a.endswith(b) if hasattr(a, "endswith") else False,
        "matches": lambda a, b: bool(re.search(b, a)) if isinstance(a, str) else False
    }
    
    def evaluate(self, condition: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """
        Evaluate a condition against data.
        
        Args:
            condition: Condition specification
            data: Data to evaluate against
            
        Returns:
            Boolean result of evaluation
        """
        # Handle empty conditions
        if not condition:
            return True
        
        # Check for logical operators (AND/OR)
        if "and" in condition:
            return all(self.evaluate(subcond, data) for subcond in condition["and"])
        
        if "or" in condition:
            return any(self.evaluate(subcond, data) for subcond in condition["or"])
        
        if "not" in condition:
            return not self.evaluate(condition["not"], data)
        
        # Handle leaf condition
        return self._evaluate_leaf_condition(condition, data)
    
    def _evaluate_leaf_condition(self, condition: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """
        Evaluate a leaf condition (no nested AND/OR).
        
        Args:
            condition: Leaf condition specification
            data: Data to evaluate against
            
        Returns:
            Boolean result of evaluation
        """
        field = condition.get("field")
        operator_str = condition.get("operator", "==")
        value = condition.get("value")
        
        # Get the field value using dot notation if needed
        field_value = self._get_field_value(data, field)

        print(f"DEBUG: Evaluating condition field='{field}', operator='{operator_str}', value='{value}', field_value='{field_value}'")

         # Ensure value is a string before calling .lower()
        if isinstance(field_value, dict):
            print(f"ERROR: field_value is a dict: {field_value}")  # ✅ Debugging Log
        elif isinstance(field_value, str) and isinstance(value, str):
            field_value = field_value.lower()
            value = value.lower()

        # Handle special case: existence check
        if operator_str == "exists":
            return field_value is not None
        
        # Handle special case: empty check
        if operator_str == "empty":
            if field_value is None:
                return True
            if hasattr(field_value, "__len__"):
                return len(field_value) == 0
            return False
        
        # Handle special case: type check
        if operator_str == "type":
            return self._check_type(field_value, value)
        
        # Handle regular operators
        if operator_str not in self.OPERATORS:
            logger.warning(f"Unknown operator: {operator_str}")
            return False
        
        # Special handling for NULL/None value
        if value == "NULL" or value == "null":
            value = None
        
        # Convert types if needed
        field_value, value = self._normalize_types(field_value, value)
        
        # Apply the operator
        try:
            return self.OPERATORS[operator_str](field_value, value)
        except Exception as e:
            logger.error(f"Error evaluating condition: {str(e)}")
            return False
    
    def _get_field_value(self, data: Dict[str, Any], field: str) -> Any:
        """Get a value from nested dictionaries using dot notation."""
        if not field or not data:
            return None
            
        parts = field.split(".")
        current = data
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None
                
        return current
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """Check if a value matches an expected type."""
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        elif expected_type == "null":
            return value is None
        return False
    
    def _normalize_types(self, left: Any, right: Any) -> tuple:
        """
        Normalize types for comparison.
        Tries to convert types to match for proper comparison.
        """
        # No conversion needed for None values
        if left is None or right is None:
            return left, right
            
        # If both are strings, no conversion needed
        if isinstance(left, str) and isinstance(right, str):
            return left, right
            
        # Try to convert strings to numbers
        if isinstance(left, (int, float)) and isinstance(right, str):
            try:
                if "." in right:
                    right = float(right)
                else:
                    right = int(right)
            except ValueError:
                pass
                
        if isinstance(right, (int, float)) and isinstance(left, str):
            try:
                if "." in left:
                    left = float(left)
                else:
                    left = int(left)
            except ValueError:
                pass
                
        # Convert between int and float
        if isinstance(left, int) and isinstance(right, float):
            left = float(left)
        elif isinstance(left, float) and isinstance(right, int):
            right = float(right)
            
        # Handle boolean values
        if isinstance(left, bool) and isinstance(right, str):
            if right.lower() == "true":
                right = True
            elif right.lower() == "false":
                right = False
                
        if isinstance(right, bool) and isinstance(left, str):
            if left.lower() == "true":
                left = True
            elif left.lower() == "false":
                left = False
                
        return left, right
