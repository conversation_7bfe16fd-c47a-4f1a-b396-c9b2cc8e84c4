f"""
V2 System Functions Models

Standardized models for V2 system function execution with uniform input/output handling.
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class FunctionCategory(str, Enum):
    """System function categories"""
    DATABASE = "database"
    VALIDATION = "validation"
    TRANSFORM = "transform"
    MATH = "math"
    UTILITY = "utility"
    TEMPORAL = "temporal"
    DATA = "data"
    CONTROL_FLOW = "control_flow"
    ADAPTER = "adapter"

class ExecutionStatus(str, Enum):
    """Function execution status"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"

class SystemFunctionInput(BaseModel):
    """
    Standardized input model for all V2 system functions.
    This ensures uniform calling mechanism across all functions.
    """
    # Core identification
    function_name: str
    go_id: Optional[str] = None
    lo_id: Optional[str] = None
    
    # Primary entity context
    primary_entity_id: Optional[str] = None
    primary_attribute_id: Optional[str] = None
    
    # All input values from LO/nested function
    input_values: Dict[str, Any] = {}
    
    # Additional context
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None
    instance_id: Optional[str] = None
    
    # Function-specific parameters (if any)
    function_params: Dict[str, Any] = {}
    
    # Execution context
    execution_context: Dict[str, Any] = {}

class SystemFunctionOutput(BaseModel):
    """
    Standardized output model for all V2 system functions.
    """
    # Execution status
    status: ExecutionStatus
    
    # Function result
    result: Any = None
    
    # Error information (if any)
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    
    # Execution metadata
    execution_time_ms: Optional[float] = None
    function_name: str
    
    # Additional output data
    metadata: Dict[str, Any] = {}
    
    # Warnings (if any)
    warnings: List[str] = []

class FunctionExecutionContext(BaseModel):
    """
    Context information for function execution
    """
    function_name: str
    category: FunctionCategory
    go_id: Optional[str] = None
    lo_id: Optional[str] = None
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None
    instance_id: Optional[str] = None
    
    # Execution tracking
    execution_id: Optional[str] = None
    parent_execution_id: Optional[str] = None
    
    # Logging context
    log_level: str = "INFO"
    trace_enabled: bool = True

class DatabaseFunctionInput(SystemFunctionInput):
    """
    Specialized input for database functions
    """
    table_name: Optional[str] = None
    entity_name: Optional[str] = None
    filters: Dict[str, Any] = {}
    data: Dict[str, Any] = {}
    
class ValidationFunctionInput(SystemFunctionInput):
    """
    Specialized input for validation functions
    """
    validation_rules: Dict[str, Any] = {}
    value_to_validate: Any = None

class TransformFunctionInput(SystemFunctionInput):
    """
    Specialized input for transformation functions
    """
    source_data: Any = None
    transform_rules: Dict[str, Any] = {}
    output_format: Optional[str] = None
