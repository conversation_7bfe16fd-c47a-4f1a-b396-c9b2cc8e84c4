"""
Send Email Function - Generic Adapter Integration
Sends emails using the existing unified adapter service with dynamic variable substitution.
"""

import requests
import json
import logging
import re
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.services.v2.base_function import BaseSystemFunction
from app.services.v2.models import SystemFunctionInput, SystemFunctionOutput, ExecutionStatus, FunctionCategory, FunctionExecutionContext

logger = logging.getLogger(__name__)

class SendEmailFunction(BaseSystemFunction):
    """
    Generic send email function that integrates with the unified adapter service.
    Supports dynamic variable substitution in subject and message templates.
    No hardcoded variables - all substitution is based on available input data.
    """
    
    def __init__(self, db_session: Session):
        super().__init__(db_session)
        self.function_name = "send_email"
    
    def get_category(self) -> FunctionCategory:
        """Return the function category"""
        return FunctionCategory.ADAPTER
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Any:
        """Execute the send email function"""
        return self._execute_function(input_data)
    
    def get_required_inputs(self) -> list[str]:
        """Define required input parameters"""
        return ["subject", "message"]  # email/to_email is handled flexibly
    
    def get_optional_inputs(self) -> list[str]:
        """Define optional input parameters - completely dynamic"""
        return ["from_email", "provider", "body_type", "priority"]
    
    def validate_inputs(self, input_data: SystemFunctionInput) -> tuple[bool, Optional[str]]:
        """Validate input parameters"""
        required_inputs = self.get_required_inputs()
        
        for required_input in required_inputs:
            if required_input not in input_data.input_values:
                return False, f"Missing required input: {required_input}"
            
            if not input_data.input_values[required_input]:
                return False, f"Required input '{required_input}' cannot be empty"
        
        # Validate email format (flexible - check both to_email and email)
        to_email = input_data.input_values.get("to_email") or input_data.input_values.get("email")
        if not to_email:
            return False, "Missing required email parameter (either 'to_email' or 'email')"
        
        # Extract simple value and validate
        email_value = self._extract_simple_value(to_email)
        if not email_value or "@" not in email_value:
            return False, f"Invalid email format. Extracted: '{email_value}' from: {to_email}"
        
        return True, None
    
    def _extract_simple_value(self, value: Any, key_path: str = None) -> str:
        """
        Extract simple string value from complex data structures.
        Handles lists of dictionaries, nested objects, etc.
        """
        if value is None:
            return ""
        
        # If it's already a simple string/number, return it
        if isinstance(value, (str, int, float, bool)):
            return str(value)
        
        # If it's a list, try to extract from first item
        if isinstance(value, list) and len(value) > 0:
            first_item = value[0]
            if isinstance(first_item, dict):
                # Try common email field names
                for email_field in ['email', 'emailAddress', 'mail', 'e_mail']:
                    if email_field in first_item:
                        return str(first_item[email_field])
                # Try common name fields
                for name_field in ['name', 'firstName', 'first_name', 'lastname', 'last_name']:
                    if name_field in first_item:
                        return str(first_item[name_field])
                # Return first available string value
                for k, v in first_item.items():
                    if isinstance(v, str) and v.strip():
                        return str(v)
            else:
                return str(first_item)
        
        # If it's a dict, try to extract relevant field
        if isinstance(value, dict):
            # Try common email field names
            for email_field in ['email', 'emailAddress', 'mail', 'e_mail']:
                if email_field in value:
                    return str(value[email_field])
            # Try common name fields
            for name_field in ['name', 'firstName', 'first_name', 'lastname', 'last_name']:
                if name_field in value:
                    return str(value[name_field])
            # Return first available string value
            for k, v in value.items():
                if isinstance(v, str) and v.strip():
                    return str(v)
        
        # Fallback to string representation
        return str(value)
    
    def _get_entity_attribute_mappings(self) -> Dict[str, str]:
        """
        Dynamically fetch entity and attribute mappings from the database.
        Returns a mapping of Entity.Attribute -> actual_field_name
        """
        try:
            from sqlalchemy import text
            query = text("""
                SELECT 
                    e.name as entity_name,
                    ea.name as attribute_name,
                    ea.attribute_id
                FROM workflow_runtime.entities e
                JOIN workflow_runtime.entity_attributes ea ON e.entity_id = ea.entity_id
                WHERE e.entity_id IN ('E1', 'E2')
                ORDER BY e.entity_id, ea.attribute_id
            """)
            
            result = self.db.execute(query).fetchall()
            mappings = {}
            
            for row in result:
                entity_name = row[0]  # e.g., 'LeaveApplication', 'Employee'
                attribute_name = row[1]  # e.g., 'firstName', 'leaveId'
                
                # Create mapping: Entity.Attribute -> attribute_name
                entity_attr_key = f"{entity_name}.{attribute_name}"
                mappings[entity_attr_key] = attribute_name
                
                # Also create lowercase version for flexibility
                mappings[entity_attr_key.lower()] = attribute_name
            
            self.logger.info(f"Built entity-attribute mappings: {mappings}")
            return mappings
            
        except Exception as e:
            self.logger.error(f"Error fetching entity-attribute mappings: {str(e)}")
            return {}
    
    def _substitute_variables(self, template: str, variables: Dict[str, Any]) -> str:
        """
        Substitute variables in template string dynamically.
        Supports formats: 
        - $variable_name
        - ${variable_name}
        - $Entity.Attribute (converts to entityAttribute)
        """
        if not template:
            return template
        
        # Pattern to match $variable_name, ${variable_name}, or $Entity.Attribute
        pattern = r'\$\{?([A-Za-z][A-Za-z0-9_]*(?:\.[A-Za-z][A-Za-z0-9_]*)?)\}?'
        
        # Get dynamic entity-attribute mappings
        entity_mappings = self._get_entity_attribute_mappings()
        
        def replace_variable(match):
            var_name = match.group(1)
            
            # Handle dot notation using dynamic mappings (e.g., Employee.firstName -> firstName)
            if '.' in var_name:
                # Try direct mapping from database
                if var_name in entity_mappings:
                    mapped_field = entity_mappings[var_name]
                    if mapped_field in variables:
                        return str(variables[mapped_field])
                
                # Try lowercase version
                if var_name.lower() in entity_mappings:
                    mapped_field = entity_mappings[var_name.lower()]
                    if mapped_field in variables:
                        return str(variables[mapped_field])
                
                # Fallback: try original format
                if var_name in variables:
                    return str(variables[var_name])
                
                # Fallback: try lowercase
                if var_name.lower() in variables:
                    return str(variables[var_name.lower()])
            
            # Direct variable lookup (exact match)
            if var_name in variables:
                return str(variables[var_name])
            
            # Try lowercase version
            if var_name.lower() in variables:
                return str(variables[var_name.lower()])
            
            # Try uppercase version
            if var_name.upper() in variables:
                return str(variables[var_name.upper()])
            
            # Log warning and return original if not found
            self.logger.warning(f"Variable '{var_name}' not found in available variables: {list(variables.keys())} or entity mappings: {list(entity_mappings.keys())}")
            return match.group(0)  # Return original placeholder
        
        result = re.sub(pattern, replace_variable, template)
        self.logger.debug(f"Variable substitution: '{template}' -> '{result}'")
        return result
    
    def _resolve_template_from_constants(self, template_name: str) -> str:
        """
        Resolve template from constants table if it's a template name.
        """
        # If it looks like a template name, try to resolve it from constants
        if template_name and template_name.endswith('_TEMPLATE'):
            try:
                from sqlalchemy import text
                query = text("""
                    SELECT value FROM workflow_runtime.constants 
                    WHERE attribute = :template_name
                """)
                result = self.db.execute(query, {"template_name": template_name}).fetchone()
                if result:
                    self.logger.info(f"Resolved template {template_name} from constants")
                    return result[0]
                else:
                    self.logger.warning(f"Template {template_name} not found in constants, using as-is")
                    return template_name
            except Exception as e:
                self.logger.error(f"Error resolving template {template_name}: {str(e)}")
                return template_name
        return template_name
    
    def _execute_function(self, input_data: SystemFunctionInput) -> Dict[str, Any]:
        """
        Execute the send email function by calling the unified adapter service.
        """
        try:
            # Extract required parameters with smart value extraction
            to_email_raw = input_data.input_values.get("to_email") or input_data.input_values.get("email")
            to_email = self._extract_simple_value(to_email_raw)
            
            # Resolve templates from constants if needed
            subject_raw = self._extract_simple_value(input_data.input_values.get("subject", "Leave Application Notification"))
            message_raw = self._extract_simple_value(input_data.input_values.get("message", "Your leave application has been processed."))
            
            subject_template = self._resolve_template_from_constants(subject_raw)
            message_template = self._resolve_template_from_constants(message_raw)
            
            # Extract optional parameters
            from_email = self._extract_simple_value(input_data.input_values.get("from_email", "<EMAIL>"))
            provider = self._extract_simple_value(input_data.input_values.get("provider", "smtp"))
            body_type = self._extract_simple_value(input_data.input_values.get("body_type", "text"))
            priority = self._extract_simple_value(input_data.input_values.get("priority", "normal"))
            
            # Validate extracted email
            if not to_email or "@" not in to_email:
                raise ValueError(f"Invalid or missing email address. Extracted: '{to_email}' from: {to_email_raw}")
            
            # Prepare ALL available variables for substitution (completely dynamic)
            substitution_vars = {}
            reserved_keys = {"to_email", "subject", "message", "from_email", "provider", "body_type", "priority"}
            
            for key, value in input_data.input_values.items():
                if key not in reserved_keys and value is not None:
                    substitution_vars[key] = value
            
            self.logger.info(f"Available variables for substitution: {list(substitution_vars.keys())}")
            
            # Perform variable substitution
            subject = self._substitute_variables(subject_template, substitution_vars)
            message = self._substitute_variables(message_template, substitution_vars)
            
            # Prepare email payload for unified adapter service
            adapter_payload = {
                "adapter_config": {
                    "service": "email",
                    "operation": "send_single",
                    "version": "v1"
                },
                "parameters": {
                    "provider": provider,
                    "to": [{"email": to_email}],
                    "subject": subject,
                    "body": message,
                    "body_type": body_type,
                    "priority": priority,
                    "reply_to": from_email,
                    "delivery_receipt": False,
                    "read_receipt": False
                }
            }
            
            # Get email service URL (use host IP from Docker container)
            email_service_url = "http://**********:8002/api/v1/send_single"
            
            # Prepare email payload for unified adapter format
            email_payload = {
                "adapter_config": {
                    "service": "email",
                    "operation": "send_single",
                    "version": "v1"
                },
                "parameters": {
                    "provider": provider,
                    "to": [{"email": to_email}],
                    "subject": subject,
                    "body": message,
                    "body_type": body_type,
                    "priority": priority,
                    "from_email": from_email
                }
            }
            
            self.logger.info(f"Sending email via email service to {to_email}")
            self.logger.debug(f"Email payload: {email_payload}")
            
            # Send email via email service
            response = requests.post(
                email_service_url,
                json=email_payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                self.logger.info(f"Email sent successfully to {to_email}")
                
                # Parse response
                try:
                    response_data = response.json()
                except:
                    response_data = {"message": "Email sent successfully"}
                
                return {
                    "success": True,
                    "message": "Email sent successfully",
                    "to_email": to_email,
                    "subject": subject,
                    "processed_message": message,
                    "adapter_response": response_data,
                    "notifiedStatus": "Yes"  # For workflow status tracking
                }
            else:
                error_message = f"Unified adapter returned status {response.status_code}: {response.text}"
                self.logger.error(error_message)
                
                raise Exception(f"ADAPTER_ERROR: {error_message}")
                
        except requests.exceptions.RequestException as e:
            error_message = f"Network error calling unified adapter: {str(e)}"
            self.logger.error(error_message)
            
            raise Exception(f"NETWORK_ERROR: {error_message}")
            
        except Exception as e:
            error_message = f"Unexpected error in send_email function: {str(e)}"
            self.logger.error(error_message)
            
            raise Exception(f"FUNCTION_ERROR: {error_message}")

# Function metadata for registration
FUNCTION_METADATA = {
    "name": "send_email",
    "description": "Send email using the unified adapter service with dynamic variable substitution",
    "category": "adapter",
    "required_inputs": ["to_email", "subject", "message"],
    "optional_inputs": ["from_email", "provider", "body_type", "priority"],
    "returns": {
        "success": {"type": "boolean"},
        "message": {"type": "string"},
        "to_email": {"type": "string"},
        "notifiedStatus": {"type": "string"}
    },
    "variable_substitution": {
        "description": "Supports dynamic variable substitution in subject and message using $variable or $Entity.Attribute format",
        "supported_formats": [
            "$variable_name",
            "${variable_name}",
            "$Entity.Attribute (converts to entityAttribute)"
        ],
        "note": "All input parameters (except reserved ones) are available for substitution"
    }
}
