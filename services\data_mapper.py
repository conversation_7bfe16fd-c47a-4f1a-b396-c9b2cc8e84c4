from typing import Dict, Any, List, Optional, Union
import logging
from copy import deepcopy

logger = logging.getLogger(__name__)

class DataMapper:
    """
    Handles data mapping operations between workflow components.
    """
    
    def map_data(self, mapping_spec: Dict[str, Any], source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map data according to mapping specification.
        
        Args:
            mapping_spec: Mapping specification
            source_data: Source data
            
        Returns:
            Mapped data
        """
        if not mapping_spec:
            return source_data.copy() if source_data else {}
        
        result = {}
        
        # Process direct mappings
        direct_mappings = mapping_spec.get("direct_mappings", {})
        for target_key, source_key in direct_mappings.items():
            result[target_key] = self._get_value_by_path(source_data, source_key)
        
        # Process transformations
        transformations = mapping_spec.get("transformations", [])
        for transform in transformations:
            target_key = transform.get("target")
            source_keys = transform.get("sources", [])
            transform_type = transform.get("type")
            
            # Get source values
            source_values = [self._get_value_by_path(source_data, key) for key in source_keys]
            
            # Apply transformation
            if transform_type == "concatenate":
                separator = transform.get("separator", " ")
                result[target_key] = separator.join([str(val) for val in source_values if val is not None])
            elif transform_type == "add":
                result[target_key] = sum([float(val) for val in source_values if val is not None])
            elif transform_type == "multiply":
                product = 1
                for val in source_values:
                    if val is not None:
                        product *= float(val)
                result[target_key] = product
            elif transform_type == "format":
                template = transform.get("template", "")
                # Create a dictionary of {index: value} for formatting
                format_values = {i: val for i, val in enumerate(source_values)}
                try:
                    result[target_key] = template.format(**format_values)
                except Exception as e:
                    logger.error(f"Error formatting template: {str(e)}")
            elif transform_type == "conditional":
                conditions = transform.get("conditions", [])
                default_value = transform.get("default")
                
                # Evaluate conditions in order
                value_set = False
                for condition in conditions:
                    condition_source = condition.get("source")
                    condition_value = self._get_value_by_path(source_data, condition_source)
                    
                    operator = condition.get("operator", "==")
                    compare_value = condition.get("value")
                    
                    if self._evaluate_condition(condition_value, operator, compare_value):
                        result[target_key] = condition.get("result")
                        value_set = True
                        break
                
                if not value_set and default_value is not None:
                    result[target_key] = default_value
        
        # Process default values
        default_values = mapping_spec.get("default_values", {})
        for key, value in default_values.items():
            if key not in result or result[key] is None:
                result[key] = value
        
        return result
    
    def _get_value_by_path(self, data: Dict[str, Any], path: str) -> Any:
        """
        Get a value from nested dictionaries using a dot-separated path.
        
        Args:
            data: Source data dictionary
            path: Dot-separated path (e.g., "user.address.city")
            
        Returns:
            Value at the specified path or None if not found
        """
        if not path or not data:
            return None
            
        parts = path.split(".")
        current = data
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None
                
        return current
    
    def _evaluate_condition(self, left: Any, operator: str, right: Any) -> bool:
        """
        Evaluate a condition between two values.
        
        Args:
            left: Left operand
            operator: Comparison operator
            right: Right operand
            
        Returns:
            Boolean result of the condition
        """
        if operator == "==":
            return left == right
        elif operator == "!=":
            return left != right
        elif operator == ">":
            return left > right
        elif operator == ">=":
            return left >= right
        elif operator == "<":
            return left < right
        elif operator == "<=":
            return left <= right
        elif operator == "in":
            return left in right if isinstance(right, (list, tuple)) else False
        elif operator == "not in":
            return left not in right if isinstance(right, (list, tuple)) else True
        elif operator == "contains":
            return right in left if isinstance(left, (str, list, tuple)) else False
        elif operator == "startswith":
            return left.startswith(right) if isinstance(left, str) else False
        elif operator == "endswith":
            return left.endswith(right) if isinstance(left, str) else False
        else:
            logger.warning(f"Unknown operator: {operator}")
            return False
