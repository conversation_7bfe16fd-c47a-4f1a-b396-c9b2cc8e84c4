from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class ObjectiveStatus(str, Enum):
    DRAFT = "Draft"
    ACTIVE = "Active"
    INACTIVE = "Inactive"
    ARCHIVED = "Archived"

class ExecutionPathwayType(str, Enum):
    SEQUENTIAL = "Sequential"
    ALTERNATIVE = "Alternative"
    PARALLEL = "Parallel"
    RECURSIVE = "Recursive"
    TERMINAL = "Terminal"

class WorkflowInstanceStatus(str, Enum):
    DRAFT = "Draft"
    ACTIVE = "Active"
    PAUSED = "Paused"
    COMPLETED = "Completed"
    FAILED = "Failed"
    CANCELLED = "Cancelled"

class ExecutionPathway(BaseModel):
    type: ExecutionPathwayType
    next_lo: Optional[str] = None
    next_los: Optional[List[str]] = None
    conditions: Optional[List[Dict[str, Any]]] = None

class InputSource(BaseModel):
    type: str
    description: Optional[str] = None
    reference: Optional[str] = None
    value: Optional[Any] = None
    function: Optional[str] = None
    values: Optional[List[Any]] = None
    dependency: Optional[Dict[str, Any]] = None

class Input(BaseModel):
    slot_id: str
    source: InputSource
    required: bool = False
    values: Optional[List[Any]] = None
    validations: Optional[Dict[str, str]] = None
    access_roles: Optional[List[str]] = None

class Output(BaseModel):
    slot_id: str
    source: Union[str, Dict[str, Any]]
    function: Optional[Dict[str, Any]] = None
    validations: Optional[Dict[str, str]] = None

class Agent(BaseModel):
    role: str
    rights: List[str]

class AgentStack(BaseModel):
    agents: List[Agent]

class InputStack(BaseModel):
    description: Optional[str] = None
    inputs: List[Input]

class OutputStack(BaseModel):
    description: Optional[str] = None
    outputs: List[Output]

class UIElement(BaseModel):
    entity_attribute: str
    ui_control: str
    helper_text: Optional[str] = None
    error_message: Optional[str] = None
    event_type: Optional[str] = None
    target: Optional[str] = None
    control_behavior: Optional[str] = None
    dependency: Optional[Dict[str, Any]] = None

class UIStack(BaseModel):
    type: str
    status: str
    description: Optional[str] = None
    overall_control: Optional[str] = None
    form_title: Optional[str] = None
    submit_button_text: Optional[str] = None
    cancel_button_text: Optional[str] = None
    additional_buttons: Optional[List[Dict[str, Any]]] = None
    elements: List[UIElement]

class TimeStack(BaseModel):
    trigger_time: Dict[str, str]
    idle_time: Dict[str, str]

class SpaceStack(BaseModel):
    location: str

class DataMapping(BaseModel):
    source: str
    target: str
    mapping_type: Optional[str] = None
    transformation: Optional[str] = None

class DataMappingStack(BaseModel):
    description: Optional[str] = None
    mappings: List[DataMapping]
    rules: Optional[List[str]] = None

class LocalObjective(BaseModel):
    id: str
    contextual_id: str
    name: str
    parent_objective_id: str
    function_type: str
    workflow_source: str
    execution_pathway: ExecutionPathway
    agent_stack: AgentStack
    input_stack: InputStack
    output_stack: OutputStack
    execution_rules: Optional[List[str]] = None
    success_message: Optional[str] = None
    ui_stack: Optional[UIStack] = None
    time_stack: Optional[TimeStack] = None
    space_stack: Optional[SpaceStack] = None
    data_mapping_stack: Optional[DataMappingStack] = None
    nested_functions: Optional[List[Dict[str, Any]]] = None
    status: ObjectiveStatus = ObjectiveStatus.ACTIVE
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: str

class GlobalObjective(BaseModel):
    tenant_id: str
    objective_id: str
    contextual_id: str
    name: str
    description: Optional[str] = None
    input_GOs: Optional[List[str]] = None
    output_GOs: Optional[List[str]] = None
    local_objectives: List[LocalObjective]
    agent_stack: Optional[AgentStack] = None
    data_mapping_stack: Optional[DataMappingStack] = None
    status: ObjectiveStatus = ObjectiveStatus.ACTIVE
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    version: str

class WorkflowInstance(BaseModel):
    instance_id: str
    go_id: str
    tenant_id: str
    status: WorkflowInstanceStatus = WorkflowInstanceStatus.DRAFT
    started_by: str
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    current_lo_id: Optional[str] = None
    instance_data: Dict[str, Any] = Field(default_factory=dict)
    is_test: bool = False
    version: str


class WorkflowExecutionLog(BaseModel):
    instance_id: str = Field(..., description="Workflow instance ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="Log timestamp")
    event_type: str = Field(..., description="Type of event (start, complete, error, etc.)")
    lo_id: Optional[str] = Field(None, description="Local Objective ID if applicable")
    go_id: Optional[str] = Field(None, description="Global Objective ID if applicable")
    details: Dict[Any, Any] = Field(default={}, description="Additional event details")
    user_id: Optional[str] = Field(None, description="User who triggered the event if applicable")
    
    class Config:
        schema_extra = {
            "example": {
                "instance_id": "wf-inst-123",
                "timestamp": "2025-03-15T14:30:00Z",
                "event_type": "lo_complete",
                "lo_id": "lo-456",
                "go_id": "go-789",
                "details": {"execution_time": 1.23, "output": {"status": "success"}},
                "user_id": "user-001"
            }
        }
