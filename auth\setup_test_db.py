"""
Setup script for the test database.

This script:
1. Creates the workflow_runtime schema if it doesn't exist
2. Runs the schema updates from the SQL script
3. Runs the data migration from the SQL script
4. Creates test data for permissions and roles
"""

import os
import sys
import logging
import bcrypt
from sqlalchemy import create_engine, text

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("setup_test_db")

# Database configuration
# Connect to the database in the Docker container
# Using localhost since we're running outside of the Docker network
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:workflow_postgres_secure_password@localhost:5433/workflow_system")

def setup_database():
    """Set up the test database."""
    logger.info("Setting up test database...")
    
    # Create database engine
    engine = create_engine(DATABASE_URL)
    
    # Create workflow_runtime schema if it doesn't exist
    with engine.connect() as conn:
        conn.execute(text("CREATE SCHEMA IF NOT EXISTS workflow_runtime"))
        conn.commit()
        logger.info("Created workflow_runtime schema")
    
    # Run schema updates
    schema_updates_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))),
        "rbac",
        "schema_updates.sql"
    )
    
    if os.path.exists(schema_updates_path):
        with open(schema_updates_path, "r") as f:
            schema_updates = f.read()
            
        # Process the SQL script to handle dollar-quoted strings and other complex SQL
        # Instead of splitting by semicolons, execute the entire script at once
        with engine.connect() as conn:
            try:
                conn.execute(text(schema_updates))
                conn.commit()
                logger.info("Ran schema updates")
            except Exception as e:
                logger.error(f"Error executing schema updates: {str(e)}")
                conn.rollback()
    else:
        logger.warning(f"Schema updates file not found: {schema_updates_path}")
    
    # Run data migration
    data_migration_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))),
        "rbac",
        "data_migration.sql"
    )
    
    if os.path.exists(data_migration_path):
        with open(data_migration_path, "r") as f:
            data_migration = f.read()
            
        # Process the SQL script to handle dollar-quoted strings and other complex SQL
        # Instead of splitting by semicolons, execute the entire script at once
        with engine.connect() as conn:
            try:
                conn.execute(text(data_migration))
                conn.commit()
                logger.info("Ran data migration")
            except Exception as e:
                logger.error(f"Error executing data migration: {str(e)}")
                conn.rollback()
    else:
        logger.warning(f"Data migration file not found: {data_migration_path}")
    
    # Create test data
    create_test_data(engine)
    
    logger.info("Database setup complete")

def create_test_data(engine):
    """Create test data for permissions and roles."""
    logger.info("Creating test data...")
    
    with engine.connect() as conn:
        # Clean up any existing test users
        try:
            # First delete from user_roles
            conn.execute(text("DELETE FROM workflow_runtime.user_roles WHERE username = 'testuser'"))
            # Then delete from users
            conn.execute(text("DELETE FROM workflow_runtime.users WHERE username = 'testuser'"))
            conn.commit()
            logger.info("Cleaned up existing test users")
        except Exception as e:
            logger.error(f"Error cleaning up test users: {str(e)}")
            conn.rollback()
        
        # Create test tenant if it doesn't exist
        conn.execute(text("""
        INSERT INTO workflow_runtime.tenants (tenant_id, name)
        VALUES ('test_tenant', 'Test Tenant')
        ON CONFLICT (tenant_id) DO NOTHING
        """))
        
        # Create test roles if they don't exist
        conn.execute(text("""
        INSERT INTO workflow_runtime.roles (role_id, name, tenant_id)
        VALUES 
            ('test_role', 'TestRole', 'test_tenant'),
            ('user_role', 'User', 'test_tenant')
        ON CONFLICT (role_id) DO NOTHING
        """))
        
        # Create test permission type if it doesn't exist
        conn.execute(text("""
        INSERT INTO workflow_runtime.permission_types (permission_id, description)
        VALUES 
            ('read', 'Permission to read resources'),
            ('write', 'Permission to write resources'),
            ('delete', 'Permission to delete resources'),
            ('execute', 'Permission to execute functions')
        ON CONFLICT (permission_id) DO NOTHING
        """))
        
        # Create test permission context if it doesn't exist
        conn.execute(text("""
        INSERT INTO workflow_runtime.permission_contexts (context_id, name, context_type, context_rules)
        VALUES (
            'test_context', 
            'Test Context', 
            'entity', 
            '{"resource_id": "test_entity", "permissions": ["read", "write"]}'::jsonb
        )
        ON CONFLICT (context_id) DO NOTHING
        """))
        
        # Assign test permission context to test role
        conn.execute(text("""
        INSERT INTO workflow_runtime.role_permissions (role_id, context_id)
        VALUES ('test_role', 'test_context')
        ON CONFLICT (role_id, context_id) DO NOTHING
        """))
        
        # Create test organizational unit if it doesn't exist
        conn.execute(text("""
        INSERT INTO workflow_runtime.organizational_units (org_unit_id, name, description, tenant_id)
        VALUES ('test_org_unit', 'Test Org Unit', 'Organizational unit for testing', 'test_tenant')
        ON CONFLICT (org_unit_id) DO NOTHING
        """))
        
        # Create test user
        try:
            # First, completely clean up any existing test user
            conn.execute(text("DELETE FROM workflow_runtime.user_roles WHERE username = 'testuser'"))
            conn.execute(text("DELETE FROM workflow_runtime.user_organizations WHERE user_id = 'test_user_id'"))
            conn.execute(text("DELETE FROM workflow_runtime.users WHERE username = 'testuser' OR user_id = 'test_user_id'"))
            conn.commit()
            
            # Create a test user with a known password
            user_id = 'test_user_id'
            # Generate a fresh bcrypt hash for 'testpassword'
            password = 'testpassword'
            salt = bcrypt.gensalt()
            password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
            logger.info(f"Generated password hash: {password_hash}")
            
            # Create the user
            conn.execute(text("""
            INSERT INTO workflow_runtime.users (user_id, username, email, password_hash, first_name, last_name, status)
            VALUES (:user_id, 'testuser', '<EMAIL>', :password_hash, 'Test', 'User', 'active')
            """), {"user_id": user_id, "password_hash": password_hash})
            
            # Assign a single role to the test user
            # The user_roles table appears to have user_id as the primary key, so we can only assign one role
            conn.execute(text("""
            INSERT INTO workflow_runtime.user_roles (user_id, username, role, tenant_id)
            VALUES (:user_id, 'testuser', 'TestRole', 'test_tenant')
            """), {"user_id": user_id})
            
            # Assign to test organizational unit
            conn.execute(text("""
            INSERT INTO workflow_runtime.user_organizations (user_id, org_unit_id, is_primary)
            VALUES (:user_id, 'test_org_unit', TRUE)
            """), {"user_id": user_id})
            
            conn.commit()
            logger.info("Created test user")
            
            # Verify the user was created correctly
            result = conn.execute(text("SELECT * FROM workflow_runtime.users WHERE username = 'testuser'")).fetchone()
            if result:
                logger.info(f"Verified test user exists: {result}")
            else:
                logger.error("Failed to verify test user exists")
        except Exception as e:
            logger.error(f"Error creating test user: {str(e)}")
            conn.rollback()
        
        conn.commit()
        
    logger.info("Test data created")

if __name__ == "__main__":
    setup_database()
