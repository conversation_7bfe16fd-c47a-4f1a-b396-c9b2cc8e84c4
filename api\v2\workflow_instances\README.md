# Workflow Instances v2 API

This module provides workflow instances functionality for the v2 API with comprehensive RBAC (Role-Based Access Control) support.

## Overview

The Workflow Instances v2 API allows users to:
- Create new workflow instances from global objectives
- Start workflow instances (transition from DRAFT to ACTIVE)
- Retrieve workflow instance details
- List workflow instances with filtering and pagination

All endpoints implement proper RBAC checks to ensure users can only access workflow instances within their tenant and based on their assigned roles.

## Architecture

The module follows the v2 microservices architecture pattern:

```
workflow_instances/
├── __init__.py          # Module exports
├── models.py            # Pydantic models for requests/responses
├── service.py           # Business logic and RBAC validation
├── routes.py            # FastAPI route definitions
└── README.md           # This documentation
```

## API Endpoints

### 1. Create Workflow Instance

**Endpoint:** `POST /v2/workflow_instances/`

Creates a new workflow instance in DRAFT status.

**Request:**
```json
{
  "go_id": "go001",
  "tenant_id": "t001", 
  "user_id": "user-001",
  "test_mode": false
}
```

**Response:**
```json
{
  "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e",
  "go_id": "go001",
  "tenant_id": "t001",
  "status": "Draft",
  "started_by": "user-001",
  "started_at": "2025-03-18T04:21:34.078548",
  "completed_at": null,
  "current_lo_id": null,
  "instance_data": {},
  "is_test": false,
  "version": "1.0"
}
```

### 2. Start Workflow Instance

**Endpoint:** `POST /v2/workflow_instances/{instance_id}/start`

Starts a workflow instance, transitioning it from DRAFT to ACTIVE status.

**Request:**
```json
{
  "user_id": "user-001"
}
```

**Response:**
```json
{
  "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e",
  "go_id": "go001",
  "tenant_id": "t001",
  "status": "Active",
  "started_by": "user-001",
  "started_at": "2025-03-18T04:21:34.078548",
  "completed_at": null,
  "current_lo_id": "4d5f8d06-ea55-52f3-abd0-2ff8cd8c2900",
  "instance_data": {},
  "is_test": false,
  "version": "1.0"
}
```

## RBAC Implementation

### Role-Based Access Control

The API implements comprehensive RBAC with the following roles:

- **admin**: Full access to all workflow instances within tenant
- **workflow_manager**: Can create, start, and manage workflow instances
- **workflow_user**: Can create and start workflow instances

### Security Context

All endpoints use the v2 security middleware (`require_v2_auth`) which provides:

- JWT token validation
- User authentication verification
- Role and permission extraction
- Tenant membership validation

## Usage Examples

### Example 1: Create and Start Workflow

```bash
# 1. Create workflow instance
curl -X POST "http://localhost:8000/api/v2/workflow_instances/?tenant_id=t001" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "go_id": "go001",
       "tenant_id": "t001", 
       "user_id": "user-001",
       "test_mode": false
     }' | jq

# 2. Start the workflow instance
curl -X POST "http://localhost:8000/api/v2/workflow_instances/INSTANCE_ID/start?tenant_id=t001" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "user-001"
     }' | jq
```

### Example 2: List Workflow Instances

```bash
# List all workflow instances for tenant
curl -X GET "http://localhost:8000/api/v2/workflow_instances/?tenant_id=t001" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq

# List with filtering
curl -X GET "http://localhost:8000/api/v2/workflow_instances/?tenant_id=t001&status=Active&page=1&page_size=20" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq
