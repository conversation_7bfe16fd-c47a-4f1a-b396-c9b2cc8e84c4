# Local Objectives v2 API - Fetch Inputs Implementation

This document provides a comprehensive overview of the Local Objectives v2 API implementation, specifically focusing on the fetch inputs functionality.

## Overview

The Local Objectives microservice provides APIs for managing local objective operations within workflow instances. The primary focus of this implementation is the **fetch inputs API** that retrieves all input fields for a workflow instance's current local objective.

## Architecture

The implementation follows the standardized v2 microservices architecture:

```
runtime/workflow-engine/app/api/v2/local_objectives/
├── __init__.py          # Module exports
├── models.py            # Pydantic models based on actual database schema
├── service.py           # Business logic with RBAC checks
├── routes.py            # FastAPI routes
└── README.md           # This documentation
```

## Key Features

### 1. Database Schema Compliance
- Models are based on the actual database schema from `current_workflow_runtime_schema.sql`
- Uses separate `entity_id`, `attribute_id`, `lo_id` fields instead of complex contextual IDs
- Supports all database columns from `lo_input_items`, `lo_input_execution`, etc.

### 2. Comprehensive Input Field Support
- **User Inputs**: Fields requiring user interaction
- **System Inputs**: Automatically calculated fields via nested functions
- **Information Fields**: Display-only fields
- **Dependent Fields**: Fields with conditional dependencies

### 3. Advanced Functionality
- **Nested Function Execution**: System fields execute nested functions for calculated values
- **Dropdown Data Sources**: Support for database queries and function-based dropdowns
- **Dependent Dropdowns**: Parent-child field relationships (e.g., leave type → leave subtype)
- **Multi-select Support**: Fields that accept multiple values
- **RBAC Integration**: Role-based access control with tenant isolation

### 4. Microservices Architecture
- Separate service classes for different concerns:
  - `LocalObjectivesService`: Main business logic
  - `RBACService`: Permission checks
  - `NestedFunctionService`: Function execution
  - `DropdownService`: Dropdown data resolution

## API Endpoints

### Main Fetch Inputs API
```
GET /api/v2/local_objectives/instances/{instance_id}/inputs?tenant_id={tenant_id}
```

**Description**: Retrieve all input fields for a workflow instance's current local objective.

**Response Structure**:
```json
{
  "local_objective": "LO_001",
  "user_inputs": [...],
  "system_inputs": [...],
  "info_inputs": [...],
  "dependent_inputs": [...],
  "dependencies": {
    "parent_field": ["child_field1", "child_field2"]
  }
}
```

### Filtered Endpoints
- `GET /instances/{instance_id}/inputs/user` - User inputs only
- `GET /instances/{instance_id}/inputs/system` - System inputs only
- `GET /instances/{instance_id}/inputs/dependencies` - Dependencies mapping only

### Health Check
- `GET /local_objectives/health` - Service health status

## Input Field Response Model

Each input field includes comprehensive metadata:

```json
{
  "id": 1,
  "item_id": "input_001",
  "input_stack_id": "stack_001",
  "slot_id": "slot_001",
  "source_type": "user",
  "required": true,
  "lo_id": "LO_001",
  "data_type": "string",
  "ui_control": "oj-input-text",
  "is_visible": true,
  "entity_id": "E1",
  "attribute_id": "A1",
  "entity_name": "LeaveApplication",
  "attribute_name": "leave_type",
  "display_name": "Leave Type",
  "enum_values": ["annual", "sick", "personal"],
  "default_value": null,
  "input_value": "annual",
  "has_dropdown_source": true,
  "dropdown_options": [
    {"value": "annual", "label": "Annual Leave"},
    {"value": "sick", "label": "Sick Leave"}
  ],
  "dependent_attribute": false,
  "dependencies": ["leave_subtype"],
  "contextual_id": "E1.A1"
}
```

## Logical Flow

### 1. Request Processing
1. **Authentication**: JWT token validation
2. **RBAC Check**: User permissions for workflow instances
3. **Tenant Validation**: User access to specified tenant
4. **Instance Lookup**: Validate workflow instance exists and get current LO

### 2. Input Field Resolution
1. **Database Query**: Fetch all input items for the current LO
2. **Categorization**: Group inputs by source type (user, system, information, dependent)
3. **Value Resolution**: Get current values from `lo_input_execution`
4. **System Calculation**: Execute nested functions for system inputs
5. **Dropdown Resolution**: Fetch dropdown options from data sources
6. **Dependency Mapping**: Build parent-child field relationships

### 3. Response Building
1. **Field Construction**: Build `InputFieldResponse` objects
2. **Categorization**: Organize into response categories
3. **Dependency Graph**: Create dependency mapping
4. **Response Assembly**: Build final `LocalObjectiveInputsResponse`

## Nested Functions Support

The implementation supports the new nested function architecture:

### Function Execution
- Functions have their own input/output stacks
- Parameters are resolved from context data
- Results are cached for performance
- Error handling with detailed logging

### Supported Function Types
- `get_current_date`: Returns current date
- `get_current_user`: Returns current user ID
- `calculate_leave_balance`: Mock leave balance calculation
- Custom functions can be easily added

## Dropdown Data Sources

### Database Queries
```sql
SELECT value_field, display_field 
FROM some_table 
WHERE condition = :parent_value
```

### Function-based Dropdowns
Functions that return arrays of `{value, label}` objects.

### Dependent Dropdowns
Parent field changes trigger child field option updates.

## Multi-select Support

Fields can accept multiple values:
```json
{
  "ui_control": "oj-select-many",
  "input_value": ["value1", "value2", "value3"],
  "enum_values": ["value1", "value2", "value3", "value4"]
}
```

## Error Handling

### HTTP Status Codes
- `200`: Success
- `401`: Unauthorized (invalid token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not found (instance not found)
- `500`: Internal server error

### Error Response Format
```json
{
  "error": "ACCESS_DENIED",
  "message": "User does not have permission to access this workflow instance",
  "details": {
    "instance_id": "uuid",
    "tenant_id": "t001"
  }
}
```

## Testing

### 1. Authentication
```bash
# Login to get token
curl -X POST "http://localhost:8000/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "testpass"}'
```

### 2. Fetch Inputs API
```bash
# Test main endpoint
curl -X GET "http://localhost:8000/api/v2/local_objectives/instances/c25bfb7d-ba2d-419c-a971-538ecafebe7e/inputs?tenant_id=t001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. Filtered Endpoints
```bash
# User inputs only
curl -X GET "http://localhost:8000/api/v2/local_objectives/instances/c25bfb7d-ba2d-419c-a971-538ecafebe7e/inputs/user?tenant_id=t001" \
  -H "Authorization: Bearer YOUR_TOKEN"

# System inputs only
curl -X GET "http://localhost:8000/api/v2/local_objectives/instances/c25bfb7d-ba2d-419c-a971-538ecafebe7e/inputs/system?tenant_id=t001" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Dependencies only
curl -X GET "http://localhost:8000/api/v2/local_objectives/instances/c25bfb7d-ba2d-419c-a971-538ecafebe7e/inputs/dependencies?tenant_id=t001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. Health Check
```bash
curl -X GET "http://localhost:8000/api/v2/local_objectives/health"
```

## Use Cases

### 1. Dynamic Form Building
Use the API response to build dynamic forms in the frontend:
- Render appropriate UI controls based on `ui_control`
- Handle required field validation
- Implement dependent dropdown logic
- Support multi-select fields

### 2. Workflow State Display
Show current state of workflow instances:
- Display current values for all fields
- Show system-calculated values
- Present information fields for context

### 3. Dependent Field Handling
Implement conditional field behavior:
- Use dependency mapping to show/hide fields
- Update dropdown options based on parent selections
- Validate dependent field requirements

### 4. Multi-select Scenarios
Support complex input scenarios:
- Multiple leave types selection
- Multiple symptoms selection
- Multiple approval workflows

## Database Schema Dependencies

The implementation relies on these key tables:
- `workflow_instances`: Workflow instance data
- `lo_input_items`: Input field definitions
- `lo_input_execution`: Current input values
- `lo_nested_functions`: System function definitions
- `dropdown_data_sources`: Dropdown configuration
- `entity_attributes`: Attribute metadata
- `user_roles`: RBAC permissions

## Logging

Extensive logging is implemented at key junctures:
- Authentication and authorization events
- Database query execution
- Nested function execution
- Error conditions
- Performance metrics

## Future Enhancements

1. **Caching**: Add Redis caching for dropdown options and system calculations
2. **Validation**: Implement comprehensive input validation
3. **Audit Trail**: Track input value changes
4. **Performance**: Optimize database queries with proper indexing
5. **Real-time Updates**: WebSocket support for live form updates

## Security Considerations

1. **RBAC**: All endpoints enforce role-based access control
2. **Tenant Isolation**: Data is strictly filtered by tenant
3. **Input Sanitization**: All inputs are validated and sanitized
4. **SQL Injection Prevention**: Parameterized queries only
5. **Rate Limiting**: Consider implementing rate limiting for production

## Monitoring

Key metrics to monitor:
- API response times
- Database query performance
- Nested function execution times
- Error rates by endpoint
- RBAC denial rates

This implementation provides a robust, scalable foundation for local objectives input management while maintaining security, performance, and maintainability standards.
