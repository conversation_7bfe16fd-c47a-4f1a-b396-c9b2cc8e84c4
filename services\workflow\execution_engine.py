import time
import logging
from typing import Dict, Any, List, Optional, Callable
from enum import Enum, auto
from uuid import uuid4
from dataclasses import dataclass, field
from sqlalchemy.orm import Session
from app.database.postgres import get_db  # ✅ Use SQLAlchemy DB session
from app.services.function_repository import function_repository  # ✅ Fix missing import
from sqlalchemy.sql import text  # ✅ Fix missing import
from app.services.claude_service import ClaudeService
from app.auth.auth_middleware import SecurityContext
from app.auth.permission.permission_service import PermissionService, PermissionContext, ResourceType, PermissionType


# Custom Exception for Workflow Execution
class WorkflowExecutionException(Exception):
    """
    Custom exception raised during workflow execution.
    Provides detailed error information about workflow processing.
    """
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        """
        Initialize the workflow execution exception.
        
        Args:
            message (str): Descriptive error message
            context (dict, optional): Additional context about the error
        """
        super().__init__(message)
        self.context = context or {}

# Enum for Execution Pathways
class ExecutionPathway(Enum):
    """Defines different types of execution pathways for Local Objectives."""
    SEQUENTIAL = auto()
    PARALLEL = auto()
    ALTERNATIVE = auto()
    RECURSIVE = auto()

@dataclass
class LocalObjective:
    """
    Represents a Local Objective (LO) in the workflow system.
    Defines a specific, atomic unit of work within a workflow.
    """
    id: str = field(default_factory=lambda: str(uuid4()))
    name: str = ""
    description: str = ""
    pathway: ExecutionPathway = ExecutionPathway.SEQUENTIAL
    
    # Stacks as defined in the architecture
    input_stack: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    output_stack: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    ui_stack: Dict[str, Any] = field(default_factory=dict)
    agent_stack: Dict[str, Any] = field(default_factory=dict)
    time_stack: Dict[str, Any] = field(default_factory=dict)
    space_stack: Dict[str, Any] = field(default_factory=dict)
    trigger_stack: Dict[str, Any] = field(default_factory=dict)
    data_mapping_stack: Dict[str, Any] = field(default_factory=dict)
    
    # Nested local objectives and system function
    nested_los: List['LocalObjective'] = field(default_factory=list)
    system_function: Optional[Callable] = None

@dataclass
class GlobalObjective:
    """
    Represents a Global Objective (GO) in the workflow system.
    Defines an end-to-end business process composed of Local Objectives.
    """
    id: str = field(default_factory=lambda: str(uuid4()))
    name: str = ""
    description: str = ""
    local_objectives: List[LocalObjective] = field(default_factory=list)




class WorkflowExecutionEngine:
    """
    Core workflow execution engine responsible for orchestrating 
    workflow execution across Global and Local Objectives.
    """
    def __init__(self, global_objective: GlobalObjective, security_context: SecurityContext = None, db: Session = None):
        """
        Initialize the workflow execution engine with a specific Global Objective.

        Args:
            global_objective (GlobalObjective): The Global Objective to be executed
            security_context (SecurityContext): Security context with user information
            db (Session, optional): SQLAlchemy database session
        """
        self.global_objective = global_objective
        self.security_context = security_context
        self.logger = logging.getLogger(self.__class__.__name__)
        self.execution_state: Dict[str, Any] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.db = db  # ✅ Store SQLAlchemy session for DB transactions
        self.claude_service = ClaudeService()

    def _validate_input_stack(self, lo: LocalObjective) -> bool:
        """
        Validate the input stack for a Local Objective.
        
        Args:
            lo (LocalObjective): Local Objective to validate inputs for
        
        Returns:
            bool: True if input stack is valid, False otherwise
        """
        if not hasattr(lo, "input_stack") or not isinstance(lo.input_stack, dict) or not lo.input_stack:
            self.logger.error(f"DEBUG: Input stack is missing or not a dictionary for LO: {lo.lo_id}")
            print(f"DEBUG: Input stack missing or incorrect format for LO: {lo.lo_id}")
            print(f"DEBUG: LO OBJECT: {lo}")
            return False
        
        try:
            print(f"DEBUG: Validating Input for LO: {lo.lo_id}")
            print(f"DEBUG: Expected Inputs: {list(lo.input_stack.keys())}")
            print(f"DEBUG: Provided Execution State: {self.execution_state}")

            for key, validators in lo.input_stack.items():
                # Get current value from execution state
                current_value = self.execution_state.get(key)

                print(f"DEBUG: Checking key '{key}': Provided Value -> {current_value}")

                # Ensure `validators` is a dictionary of functions
                if not isinstance(validators, dict):
                    self.logger.error(f"DEBUG: Invalid validators format for key '{key}': {validators}")
                    print(f"DEBUG: Invalid validators format for key '{key}': {validators}")
                    return False

                # Run all validation functions for this input
                for validator_name, validator_func in validators.items():
                    if not callable(validator_func):  # ✅ Ensure it's a function before calling
                        self.logger.warning(f"DEBUG: Validator '{validator_name}' is not callable for key '{key}', skipping...")
                        print(f"DEBUG: Skipping non-callable validator '{validator_name}' for key '{key}'")
                        continue  # ✅ SKIP instead of failing
                    
                    if not validator_func(current_value):
                        self.logger.error(f"Input validation failed for {key} on {validator_name}")
                        print(f"DEBUG: Validation failed for key '{key}' using '{validator_name}'")
                        return False

            return True
        except Exception as e:
            self.logger.error(f"Input stack validation error: {e}")
            print(f"DEBUG: Exception in validation: {e}")
            return False

    async def _get_claude_assistance(self, lo: LocalObjective, context_data: dict = None) -> Dict[str, Any]:
        """
        Get AI assistance from Claude for a Local Objective.
        
        Args:
            lo (LocalObjective): Local Objective to get assistance for
            context_data (dict, optional): Additional context data
            
        Returns:
            Dict[str, Any]: Claude's response and suggestions
        """
        try:
            # Prepare context for Claude
            context = {
                "workflow_id": self.global_objective.go_id if hasattr(self.global_objective, "go_id") else "unknown",
                "objective_name": self.global_objective.name,
                "current_step": lo.name,
                "variables": self.execution_state,
                "execution_history": self.execution_history
            }
            
            # Add any additional context
            if context_data:
                context.update(context_data)
                
            # Prepare message for Claude
            user_message = f"""
            I'm executing the Local Objective '{lo.name}' with the following details:
            
            - System Function: {lo.system_function}
            - Input Stack: {lo.input_stack}
            - Current Execution State: {self.execution_state}
            
            Please analyze this workflow step and provide suggestions or insights.
            """
            
            # Query Claude
            claude_response = await self.claude_service.query_claude(
                user_message=user_message,
                workflow_context=context
            )
            
            self.logger.info(f"Claude provided assistance for LO: {lo.name}")
            
            return {
                "claude_assistance": claude_response,
                "lo_name": lo.name,
                "timestamp": time.time()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting Claude assistance: {e}")
            return {"claude_assistance": f"Error: {str(e)}", "error": True}

    def _execute_local_objective(self, lo: LocalObjective) -> Dict[str, Any]:
        """
        Execute a single Local Objective.

        Args:
            lo (LocalObjective): Local Objective to execute

        Returns:
            Dict[str, Any]: Output from the execution
        """
        start_time = time.time()

        # ✅ Debugging: Print LO details
        print(f"DEBUG: Executing Local Objective: {lo.lo_id} | Function: {lo.system_function}")
        self.logger.info(f"DEBUG: Executing Local Objective: {lo.lo_id} | Function: {lo.system_function}")


        # Validate input stack
        if not self._validate_input_stack(lo):
            raise WorkflowExecutionException(f"Input validation failed for Local Objective: {lo.name}")
        
        # Check if AI assistance is enabled for this LO
        if hasattr(lo, "use_ai_assistance") and lo.use_ai_assistance:
            try:
                # This needs to be awaited in an async context
                # For now, we'll use synchronous code since your engine is synchronous
                import asyncio
                claude_response = asyncio.run(self._get_claude_assistance(lo))
                
                # Store Claude's insights in execution state
                self.execution_state["ai_insights"] = claude_response.get("claude_assistance")
                
                # Log that we used AI assistance
                self.logger.info(f"Used AI assistance for LO: {lo.name}")
                
            except Exception as e:
                self.logger.warning(f"Failed to get AI assistance, continuing execution: {e}")

        # ✅ Check if system_function is None
        if lo.system_function is None:
            raise WorkflowExecutionException(f"System function is missing for Local Objective: {lo.name}")


        # Check RBAC before execution
        if not self._check_rbac(lo):
            raise WorkflowExecutionException(f"Unauthorized function execution: {lo.system_function}")

        output = {}

        # Execute system function if defined
        if lo.system_function:
            try:
                # Prepare function input from current execution state
                function_input = {key: self.execution_state.get(key) for key in lo.input_stack.keys()}

                # Optionally get AI assistance for function execution
                if hasattr(lo, "ai_function_guidance") and lo.ai_function_guidance:
                    import asyncio
                    guidance = asyncio.run(self._get_claude_assistance(lo, {"function_input": function_input}))
                    
                    # Apply any AI suggestions if configured to do so
                    if hasattr(lo, "apply_ai_suggestions") and lo.apply_ai_suggestions and "suggestions" in guidance:
                        # Here you would parse and apply Claude's suggestions
                        self.logger.info(f"Applying AI suggestions for function: {lo.system_function}")
                

                # Log function execution
                self.logger.info(f"Executing function {lo.system_function} with input {function_input}")

                # Retrieve function from repository and execute with retries
                #function = function_repository.functions[lo.system_function]

                # Check if lo.system_function contains category and function name
                if "." in lo.system_function:
                    category, function_name = lo.system_function.split(".", 1)
                    function = function_repository.get_function(category, function_name)
                else:
                    # Handle case where the format might be different
                    self.logger.error(f"Invalid function format: {lo.system_function}")
                    raise ValueError(f"Function format must be 'category.function_name': {lo.system_function}")

                output = self._execute_with_retries(function, retries=3, **function_input)

                # ✅ Ensure execution state updates only if function execution succeeds
                self.execution_state.update(output)

                # Log execution
                self.execution_history.append({
                    "local_objective": lo.name,
                    "input": function_input,
                    "output": output
                })

            except Exception as e:
                self.logger.error(f"Execution failed for LO {lo.name}: {e}")
                raise WorkflowExecutionException(f"System function execution error in LO {lo.name}")

        # Handle nested Local Objectives based on execution pathway
        if lo.nested_los:
            if lo.pathway == ExecutionPathway.SEQUENTIAL:
                output.update(self._execute_sequential_los(lo.nested_los))
            elif lo.pathway == ExecutionPathway.PARALLEL:
                output.update(self._execute_parallel_los(lo.nested_los))
            elif lo.pathway == ExecutionPathway.ALTERNATIVE:
                output.update(self._execute_alternative_los(lo.nested_los))
            elif lo.pathway == ExecutionPathway.RECURSIVE:
                output.update(self._execute_recursive_los(lo.nested_los))

        # Track execution time
        self._track_execution_time(lo.name, start_time)

        return output

    


    def _execute_sequential_los(self, los: List[LocalObjective]) -> Dict[str, Any]:
        """
        Execute Local Objectives sequentially.
        
        Args:
            los (List[LocalObjective]): List of Local Objectives to execute
        
        Returns:
            Dict[str, Any]: Aggregated output from all LOs
        """
        final_output = {}
        for lo in los:
            output = self._execute_local_objective(lo)
            final_output.update(output)
        return final_output

    def _execute_parallel_los(self, los: List[LocalObjective]) -> Dict[str, Any]:
        """
        Execute Local Objectives in parallel.
        
        Args:
            los (List[LocalObjective]): List of Local Objectives to execute
        
        Returns:
            Dict[str, Any]: Aggregated output from all LOs
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed

        final_output = {}
        with ThreadPoolExecutor() as executor:
            futures = {executor.submit(self._execute_local_objective, lo): lo for lo in los}
            for future in as_completed(futures):
                lo = futures[future]
                try:
                    output = future.result()
                    final_output.update(output)
                except Exception as e:
                    self.logger.error(f"Parallel execution failed for LO {lo.name}: {e}")
        return final_output
    
    def _execute_alternative_los(self, los: List[LocalObjective]) -> Dict[str, Any]:
        """
        Execute one Local Objective from the list based on conditions.

        Args:
            los (List[LocalObjective]): List of Local Objectives to choose from.

        Returns:
            Dict[str, Any]: Output from the selected Local Objective.
        """
        for lo in los:
            condition = lo.trigger_stack.get("condition", lambda: False)  # Default condition always False
            if condition():
                return self._execute_local_objective(lo)
        
        self.logger.warning("No condition matched in Alternative Execution Pathway.")
        return {}

    
    def _execute_recursive_los(self, lo: LocalObjective, max_depth: int = 10) -> Dict[str, Any]:
        """
        Recursively execute a Local Objective up to a defined depth.

        Args:
            lo (LocalObjective): Local Objective to execute recursively.
            max_depth (int): Maximum recursion depth to prevent infinite loops.

        Returns:
            Dict[str, Any]: Final output after recursion.
        """
        depth = 0
        output = {}

        while depth < max_depth:
            output = self._execute_local_objective(lo)
            if not lo.trigger_stack.get("continue_condition", lambda: False)():
                break
            depth += 1

        return output

    
    def _execute_with_retries(self, function, retries=3, **kwargs):
        """
        Execute a function with a retry mechanism.

        Args:
            function (Callable): Function to execute.
            retries (int): Number of retry attempts.
            kwargs: Arguments for function execution.

        Returns:
            Any: Function output.
        """
        for attempt in range(retries):
            try:
                return function(**kwargs)
            except Exception as e:
                self.logger.error(f"Attempt {attempt + 1}: Execution failed - {str(e)}")
                if attempt == retries - 1:
                    raise WorkflowExecutionException(f"Function {function.__name__} failed after {retries} retries")



    def _execute_in_transaction(self, lo: LocalObjective):
        """
        Execute Local Objective in a database transaction.

        Args:
            lo (LocalObjective): Local Objective to execute.

        Returns:
            Dict[str, Any]: Execution output.
        """
        if not self.db:
            raise WorkflowExecutionException("Database session is not available.")

        try:
            self.logger.info(f"Starting transaction for LO {lo.name}")
            
            # Execute the LO
            output = self._execute_local_objective(lo)

            self.db.commit()  # ✅ Commit transaction
            self.logger.info(f"Transaction committed for LO {lo.name}")

            return output

        except Exception as e:
            self.db.rollback()  # ✅ Rollback on failure
            self.logger.error(f"Transaction failed for LO {lo.name}: {e}")
            raise WorkflowExecutionException(f"Transaction failed for {lo.name}")


    import time

    def _track_execution_time(self, function_name: str, start_time: float):
        """
        Log execution time for a function.

        Args:
            function_name (str): Name of the function.
            start_time (float): Start time.

        Returns:
            None
        """
        execution_time = time.time() - start_time
        self.logger.info(f"Execution time for {function_name}: {execution_time:.2f} seconds")

    USER_ROLES = {
        "HRManager": ["create", "apply_leave", "approve_leave", "reject_leave"],
        "Manager": ["approve_leave", "reject_leave"],
        "Employee": ["create", "apply_leave"],
        "System": ["create", "auto_approve"]
    }



    def _check_rbac(self, lo: LocalObjective) -> bool:
        """
        Check if the user has permission to execute a Local Objective.

        Args:
            lo (LocalObjective): Local Objective to check permission for

        Returns:
            bool: True if access is granted, False otherwise
        """
        # If no security context, deny access
        if not self.security_context or not self.security_context.authenticated:
            self.logger.warning("No security context or user not authenticated")
            return False
            
        # If user has Administrator role, grant access
        if "Administrator" in self.security_context.roles:
            self.logger.info("User has Administrator role, granting access")
            return True
            
        # Check permission using permission service
        try:
            # Create permission service
            permission_service = PermissionService(self.db)
            
            # Create permission context
            permission_context = PermissionContext(
                resource_type=ResourceType.FUNCTION,
                resource_id=lo.id,
                permission_type=PermissionType.EXECUTE,
                tenant_id=self.security_context.tenant_id
            )
            
            # Check permission
            has_permission = permission_service.check_permission(
                self.security_context, 
                permission_context
            )
            
            if has_permission:
                self.logger.info(f"✅ ACCESS GRANTED for user {self.security_context.username} to LO {lo.id}")
            else:
                self.logger.warning(f"❌ ACCESS DENIED for user {self.security_context.username} to LO {lo.id}")
                
            return has_permission
            
        except Exception as e:
            self.logger.error(f"Error checking RBAC: {str(e)}")
            return False







    def execute(self) -> Dict[str, Any]:
        """
        Execute the entire Global Objective workflow.
        
        Returns:
            Dict[str, Any]: Final execution state after workflow completion
        """
        try:
            # Reset execution state and history
            self.execution_state.clear()
            self.execution_history.clear()
            
            # Execute Local Objectives in the Global Objective
            for lo in self.global_objective.local_objectives:
                self._execute_local_objective(lo)
            
            return self.execution_state
        
        except Exception as e:
            self.logger.error(f"Global Objective execution failed: {e}")
            raise WorkflowExecutionException(
                f"Workflow execution error: {e}", 
                {"global_objective": self.global_objective.name}
            )
