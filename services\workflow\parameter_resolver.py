import logging
from typing import Dict, Any, Optional, List
from app.database.postgres import get_db
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
import json
from app.auth.auth_middleware import SecurityContext

class ParameterResolver:
    """
    Resolves parameters for workflow execution based on various sources.
    """
    
    def __init__(self, db: Optional[Session] = None, security_context: SecurityContext = None):
        """
        Initialize the parameter resolver.
        
        Args:
            db (Session, optional): SQLAlchemy database session
            security_context (SecurityContext, optional): Security context with user information
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.db = db or next(get_db())
        self.security_context = security_context
    
    def resolve_parameters(self, parameter_definitions: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Resolve parameters based on their definitions and context.
        
        Args:
            parameter_definitions (Dict[str, Any]): Parameter definitions
            context (Dict[str, Any], optional): Additional context for parameter resolution
            
        Returns:
            Dict[str, Any]: Resolved parameters
        """
        resolved_params = {}
        context = context or {}
        
        # Add security context to resolution context if available
        if self.security_context:
            context['user'] = {
                'user_id': self.security_context.user_id,
                'username': self.security_context.username,
                'email': self.security_context.email,
                'roles': self.security_context.roles,
                'tenant_id': self.security_context.tenant_id
            }
        
        for param_name, param_def in parameter_definitions.items():
            resolved_params[param_name] = self._resolve_parameter(param_name, param_def, context)
            
        return resolved_params
    
    def _resolve_parameter(self, param_name: str, param_def: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """
        Resolve a single parameter based on its definition and context.
        
        Args:
            param_name (str): Parameter name
            param_def (Dict[str, Any]): Parameter definition
            context (Dict[str, Any]): Resolution context
            
        Returns:
            Any: Resolved parameter value
        """
        # Get parameter source
        source = param_def.get('source', 'default')
        
        # Resolve based on source
        if source == 'context':
            return self._resolve_from_context(param_def, context)
        elif source == 'database':
            return self._resolve_from_database(param_def)
        elif source == 'function':
            return self._resolve_from_function(param_def, context)
        elif source == 'security_context':
            return self._resolve_from_security_context(param_def)
        else:
            # Default value
            return param_def.get('default_value')
    
    def _resolve_from_context(self, param_def: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """
        Resolve parameter from context.
        
        Args:
            param_def (Dict[str, Any]): Parameter definition
            context (Dict[str, Any]): Resolution context
            
        Returns:
            Any: Resolved parameter value
        """
        path = param_def.get('path', '')
        default = param_def.get('default_value')
        
        # Navigate through context using path
        value = context
        for key in path.split('.'):
            if not key:
                continue
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def _resolve_from_database(self, param_def: Dict[str, Any]) -> Any:
        """
        Resolve parameter from database.
        
        Args:
            param_def (Dict[str, Any]): Parameter definition
            
        Returns:
            Any: Resolved parameter value
        """
        query = param_def.get('query', '')
        params = param_def.get('params', {})
        default = param_def.get('default_value')
        
        try:
            result = self.db.execute(text(query), params).fetchone()
            if result:
                return result[0]
            return default
        except Exception as e:
            self.logger.error(f"Database parameter resolution error: {e}")
            return default
    
    def _resolve_from_function(self, param_def: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """
        Resolve parameter using a function.
        
        Args:
            param_def (Dict[str, Any]): Parameter definition
            context (Dict[str, Any]): Resolution context
            
        Returns:
            Any: Resolved parameter value
        """
        function_name = param_def.get('function', '')
        args = param_def.get('args', {})
        default = param_def.get('default_value')
        
        # Resolve function arguments from context
        resolved_args = {}
        for arg_name, arg_def in args.items():
            resolved_args[arg_name] = self._resolve_parameter(arg_name, arg_def, context)
        
        try:
            # Import and execute function
            module_path, func_name = function_name.rsplit('.', 1)
            module = __import__(module_path, fromlist=[func_name])
            func = getattr(module, func_name)
            return func(**resolved_args)
        except Exception as e:
            self.logger.error(f"Function parameter resolution error: {e}")
            return default
    
    def _resolve_from_security_context(self, param_def: Dict[str, Any]) -> Any:
        """
        Resolve parameter from security context.
        
        Args:
            param_def (Dict[str, Any]): Parameter definition
            
        Returns:
            Any: Resolved parameter value
        """
        if not self.security_context:
            return param_def.get('default_value')
        
        field = param_def.get('field', '')
        default = param_def.get('default_value')
        
        if field == 'user_id':
            return self.security_context.user_id
        elif field == 'username':
            return self.security_context.username
        elif field == 'email':
            return self.security_context.email
        elif field == 'roles':
            return self.security_context.roles
        elif field == 'tenant_id':
            return self.security_context.tenant_id
        elif field == 'permissions':
            return self.security_context.permissions
        else:
            return default
