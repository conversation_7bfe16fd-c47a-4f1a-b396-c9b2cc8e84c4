#!/usr/bin/env python3
"""
Test script to verify that input_value JSONB column gets populated correctly
"""

import sys
import os
import logging
import json
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_jsonb_upsert():
    """Test the UPSERT logic for JSONB input_value column"""
    
    try:
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        from core.config import settings
        
        # Build connection string
        connection_string = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
        
        engine = create_engine(connection_string)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        logger.info("🔗 Connected to database successfully")
        
        # Test data
        test_instance_id = "test-fix-instance-001"
        test_lo_id = "test-fix-lo-001"
        test_user_id = "test-fix-user-001"
        
        test_cases = [
            {
                "contextual_id": "test_string_field",
                "value": "Hello World",
                "description": "Simple string value"
            },
            {
                "contextual_id": "test_number_field", 
                "value": 42,
                "description": "Integer value"
            },
            {
                "contextual_id": "test_boolean_field",
                "value": True,
                "description": "Boolean value"
            },
            {
                "contextual_id": "test_object_field",
                "value": {"name": "John", "age": 30, "active": True},
                "description": "JSON object"
            },
            {
                "contextual_id": "test_array_field",
                "value": [1, 2, "three", {"four": 4}],
                "description": "JSON array"
            },
            {
                "contextual_id": "test_null_field",
                "value": None,
                "description": "NULL value"
            }
        ]
        
        logger.info(f"🧪 Testing UPSERT with {len(test_cases)} test cases...")
        
        # Clean up any existing test data
        cleanup_query = """
        DELETE FROM workflow_runtime.lo_input_execution 
        WHERE instance_id = :instance_id AND lo_id = :lo_id
        """
        db.execute(text(cleanup_query), {
            "instance_id": test_instance_id,
            "lo_id": test_lo_id
        })
        db.commit()
        logger.info("🧹 Cleaned up existing test data")
        
        # Test the UPSERT logic
        upsert_query = """
        INSERT INTO workflow_runtime.lo_input_execution 
        (instance_id, lo_id, input_contextual_id, input_value, created_at, created_by, updated_at, updated_by)
        VALUES (:instance_id, :lo_id, :contextual_id, :value, NOW(), :user_id, NOW(), :user_id)
        ON CONFLICT (instance_id, lo_id, input_contextual_id) 
        DO UPDATE SET 
            input_value = EXCLUDED.input_value,
            updated_at = NOW(),
            updated_by = EXCLUDED.updated_by
        """
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n--- Test Case {i}: {test_case['description']} ---")
            logger.info(f"Value: {test_case['value']} (type: {type(test_case['value'])})")
            
            try:
                # Execute UPSERT
                params = {
                    "instance_id": test_instance_id,
                    "lo_id": test_lo_id,
                    "contextual_id": test_case['contextual_id'],
                    "value": test_case['value'],
                    "user_id": test_user_id
                }
                
                rows_affected = db.execute(text(upsert_query), params).rowcount
                logger.info(f"Rows affected: {rows_affected}")
                
                # Verify the stored value
                verify_query = """
                SELECT input_value FROM workflow_runtime.lo_input_execution
                WHERE instance_id = :instance_id AND lo_id = :lo_id AND input_contextual_id = :contextual_id
                """
                result = db.execute(text(verify_query), {
                    "instance_id": test_instance_id,
                    "lo_id": test_lo_id,
                    "contextual_id": test_case['contextual_id']
                }).fetchone()
                
                if result is not None:
                    stored_value = result.input_value
                    logger.info(f"Stored value: {stored_value} (type: {type(stored_value)})")
                    
                    # Check if the value was stored correctly
                    if test_case['value'] is None:
                        if stored_value is None:
                            logger.info("✅ NULL value stored correctly")
                            success_count += 1
                        else:
                            logger.error(f"❌ Expected NULL but got: {stored_value}")
                    else:
                        # For non-NULL values, compare the actual data
                        if stored_value == test_case['value']:
                            logger.info("✅ Value stored correctly")
                            success_count += 1
                        else:
                            logger.error(f"❌ Value mismatch. Expected: {test_case['value']}, Got: {stored_value}")
                else:
                    logger.error("❌ No record found after UPSERT")
                    
            except Exception as e:
                logger.error(f"❌ Test case failed: {str(e)}")
        
        # Commit all changes
        db.commit()
        
        logger.info(f"\n🎯 Test Summary:")
        logger.info(f"✅ Successful: {success_count}/{len(test_cases)}")
        logger.info(f"❌ Failed: {len(test_cases) - success_count}/{len(test_cases)}")
        
        if success_count == len(test_cases):
            logger.info("🎉 All tests passed! JSONB UPSERT is working correctly.")
            return True
        else:
            logger.error("💥 Some tests failed. Check the logs above.")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test setup failed: {str(e)}")
        return False
    finally:
        if 'db' in locals():
            db.close()

def main():
    """Run the test"""
    logger.info("🚀 Starting JSONB input_value fix verification test...")
    
    success = test_jsonb_upsert()
    
    if success:
        logger.info("\n✅ JSONB fix verification completed successfully!")
        logger.info("The lo_input_execution table should now store values correctly.")
    else:
        logger.error("\n❌ JSONB fix verification failed!")
        logger.error("There are still issues with storing values in lo_input_execution.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
