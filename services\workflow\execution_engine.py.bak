from app.services.data_mapper import DataMapper
from app.services.condition_evaluator import ConditionEvaluator
from app.services.system_functions import function_repository
from app.services.validator import Validator, ValidationError
from typing import Dict, Any, List
from bson import ObjectId
from app.database.mongo import mongodb_conn
from app.database.postgres import SessionLocal
from app.models.workflow import (
    GlobalObjective, 
    LocalObjective, 
    WorkflowExecutionLog
)
from sqlalchemy.orm import Session
from datetime import datetime

class WorkflowService:
    def __init__(self):
        """
        Initialize Workflow Service with MongoDB and PostgreSQL connections
        """
        self.mongo_db = mongodb_conn.get_database()

    def create_global_objective(
        self, 
        tenant_id: str, 
        name: str, 
        description: str = "", 
        configuration: Dict[str, Any] = None
    ) -> GlobalObjective:
        """
        Create a new Global Objective
        
        Args:
            tenant_id (str): Tenant identifier
            name (str): Name of the global objective
            description (str, optional): Description of the global objective
            configuration (dict, optional): Additional configuration details
        
        Returns:
            GlobalObjective: Created global objective
        """
        global_objective_collection = self.mongo_db['global_objectives']
        
        global_objective_data = {
            "tenant_id": tenant_id,
            "objective_id": str(ObjectId()),
            "name": name,
            "description": description,
            "configuration": configuration or {},
            "local_objectives": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_active": True
        }
        
        result = global_objective_collection.insert_one(global_objective_data)
        global_objective_data['_id'] = result.inserted_id
        
        return GlobalObjective(**global_objective_data)

    def create_local_objective(
        self, 
        tenant_id: str, 
        global_objective_id: str, 
        name: str, 
        description: str = "", 
        order: int = 0,
        configuration: Dict[str, Any] = None
    ) -> LocalObjective:
        """
        Create a new Local Objective within a Global Objective
        
        Args:
            tenant_id (str): Tenant identifier
            global_objective_id (str): Parent Global Objective ID
            name (str): Name of the local objective
            description (str, optional): Description of the local objective
            order (int, optional): Execution order within the global objective
            configuration (dict, optional): Additional configuration details
        
        Returns:
            LocalObjective: Created local objective
        """
        local_objectives_collection = self.mongo_db['local_objectives']
        global_objectives_collection = self.mongo_db['global_objectives']
        
        # Verify global objective exists
        global_objective = global_objectives_collection.find_one({
            "tenant_id": tenant_id, 
            "objective_id": global_objective_id
        })
        
        if not global_objective:
            raise ValueError(f"Global Objective {global_objective_id} not found for tenant {tenant_id}")
        
        local_objective_data = {
            "tenant_id": tenant_id,
            "objective_id": str(ObjectId()),
            "global_objective_id": global_objective_id,
            "name": name,
            "description": description,
            "order": order,
            "configuration": configuration or {},
            "nested_functions": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_active": True
        }
        
        result = local_objectives_collection.insert_one(local_objective_data)
        local_objective_data['_id'] = result.inserted_id
        
        # Update global objective to include this local objective
        global_objectives_collection.update_one(
            {"objective_id": global_objective_id},
            {"$push": {"local_objectives": local_objective_data['objective_id']}}
        )
        
        return LocalObjective(**local_objective_data)

    def log_workflow_execution(
        self, 
        global_objective_id: str, 
        local_objective_id: str, 
        tenant_id: str, 
        status: str, 
        execution_metadata: Dict[str, Any] = None
    ) -> WorkflowExecutionLog:
        """
        Log workflow execution details in PostgreSQL
        
        Args:
            global_objective_id (str): Global Objective ID
            local_objective_id (str): Local Objective ID
            tenant_id (str): Tenant identifier
            status (str): Execution status
            execution_metadata (dict, optional): Additional execution metadata
        
        Returns:
            WorkflowExecutionLog: Created workflow execution log
        """
        db: Session = SessionLocal()
        
        try:
            execution_log = WorkflowExecutionLog(
                global_objective_id=global_objective_id,
                local_objective_id=local_objective_id,
                tenant_id=tenant_id,
                status=status,
                started_at=datetime.utcnow(),
                execution_metadata=execution_metadata or {}
            )
            
            db.add(execution_log)
            db.commit()
            db.refresh(execution_log)
            
            return execution_log
        finally:
            db.close()

    def get_global_objectives(
        self, 
        tenant_id: str, 
        is_active: bool = True
    ) -> List[GlobalObjective]:
        """
        Retrieve Global Objectives for a tenant
        
        Args:
            tenant_id (str): Tenant identifier
            is_active (bool, optional): Filter by active status
        
        Returns:
            List[GlobalObjective]: List of global objectives
        """
        global_objectives_collection = self.mongo_db['global_objectives']
        
        query = {"tenant_id": tenant_id}
        if is_active is not None:
            query['is_active'] = is_active
        
        global_objectives = list(global_objectives_collection.find(query))
        
        return [GlobalObjective(**obj) for obj in global_objectives]



# In app/services/workflow/execution_engine.py
# Add or update the WorkflowExecutionEngine class

class WorkflowExecutionEngine:
    """Engine responsible for executing workflow instances."""
    
    def __init__(self, db=None, function_repository=None):
        self.db = db or mongodb_conn.get_database()
        self.function_repository = function_repository
        self.pg_session = SessionLocal()
        self.data_mapper = DataMapper()
        self.condition_evaluator = ConditionEvaluator()
        self.validator = Validator()
    
    def _load_workflow_definition(self, workflow_id):
        """Load workflow definition from MongoDB."""
        workflow = self.db.workflows.find_one({"_id": ObjectId(workflow_id)})
        if not workflow:
            raise WorkflowExecutionException(f"Workflow with ID {workflow_id} not found")
        return workflow
    
    def _create_workflow_instance(self, workflow_id, input_data=None):
        """Create a new workflow instance in PostgreSQL."""
        # Store instance in PostgreSQL for runtime state
        instance = {
            "workflow_id": workflow_id,
            "status": "started",
            "input_data": input_data or {},
            "start_time": datetime.now(),
            "current_state": {
                "active_gos": [],
                "active_los": [],
                "completed_gos": [],
                "completed_los": [],
                "execution_path": []
            }
        }
        # Insert into PostgreSQL and get ID
        # Simplified for now, actual implementation would use SQLAlchemy
        return "instance-" + str(ObjectId())
    
    def _log_execution_event(self, instance_id, event_type, go_id=None, lo_id=None, details=None):
        """Log workflow execution events."""
        log = WorkflowExecutionLog(
            instance_id=instance_id,
            event_type=event_type,
            go_id=go_id,
            lo_id=lo_id,
            details=details or {}
        )
        # In a real implementation, save to database
        print(f"LOG: {log}")
        return log
    
    def execute_workflow(self, workflow_id, input_data=None):
        """Execute a full workflow."""
        try:
            # Load workflow definition
            workflow = self._load_workflow_definition(workflow_id)
            
            # Create workflow instance
            instance_id = self._create_workflow_instance(workflow_id, input_data)
            
            # Log start event
            self._log_execution_event(instance_id, "workflow_start", details={"input_data": input_data})
            
            # Find initial GO to execute
            initial_go = self._determine_initial_go(workflow)
            
            # Execute initial GO
            go_result = self.execute_global_objective(instance_id, initial_go["go_id"], input_data)
            
            # Update workflow status
            final_status = "completed" if go_result["status"] == "completed" else "failed"
            
            # Log completion
            self._log_execution_event(
                instance_id, 
                f"workflow_{final_status}", 
                details={"result": go_result}
            )
            
            return {
                "workflow_id": workflow_id,
                "instance_id": instance_id,
                "status": final_status,
                "result": go_result
            }
            
        except Exception as e:
            self._log_execution_event(
                instance_id if 'instance_id' in locals() else None,
                "workflow_error",
                details={"error": str(e)}
            )
            raise WorkflowExecutionException(
                f"Failed to execute workflow: {str(e)}",
                workflow_id=workflow_id
            )
    
    def _determine_initial_go(self, workflow):
        """Determine the initial Global Objective to execute."""
        # For now, just return the first GO
        if not workflow.get("global_objectives"):
            raise WorkflowExecutionException("Workflow has no Global Objectives defined")
        return workflow["global_objectives"][0]
    
    def execute_global_objective(self, instance_id, go_id, input_data=None):
        """Execute a Global Objective."""
        try:
            # Log start
            self._log_execution_event(instance_id, "go_start", go_id=go_id)
            
            # Load GO definition
            go = self._load_go_definition(go_id)
            
            # Process input stack validations
            validated_input = self._process_input_stack(go.get("input_stack", {}), input_data)
            
            # Execute first LO
            initial_lo = self._determine_initial_lo(go)
            lo_result = self.execute_local_objective(instance_id, go_id, initial_lo["lo_id"], validated_input)
            
            # Determine next steps based on LO result and trigger stack
            # For now, simplified implementation
            
            # Apply output stack transformations
            output = self._process_output_stack(go.get("output_stack", {}), lo_result)
            
            # Log completion
            self._log_execution_event(
                instance_id, 
                "go_complete", 
                go_id=go_id, 
                details={"result": output}
            )
            
            return {
                "go_id": go_id,
                "status": "completed",
                "result": output
            }
            
        except Exception as e:
            self._log_execution_event(
                instance_id,
                "go_error",
                go_id=go_id,
                details={"error": str(e)}
            )
            raise WorkflowExecutionException(
                f"Failed to execute Global Objective: {str(e)}",
                go_id=go_id
            )
    
    def _load_go_definition(self, go_id):
        """Load Global Objective definition."""
        # For now, simplified implementation
        go = self.db.global_objectives.find_one({"go_id": go_id})
        if not go:
            raise WorkflowExecutionException(f"Global Objective with ID {go_id} not found")
        return go
    
    def _determine_initial_lo(self, go):
        """Determine the initial Local Objective to execute."""
        if not go.get("local_objectives"):
            raise WorkflowExecutionException("Global Objective has no Local Objectives defined")
        return go["local_objectives"][0]
    
    def execute_local_objective(self, instance_id, go_id, lo_id, input_data=None):
        """Execute a Local Objective."""
        try:
            # Log start
            self._log_execution_event(
                instance_id, 
                "lo_start", 
                go_id=go_id, 
                lo_id=lo_id
            )
            
            # Load LO definition
            lo = self._load_lo_definition(lo_id)
            
            # Check for nested LOs
            if lo.get("local_objectives"):
                result = self._execute_nested_los(instance_id, go_id, lo, input_data)
            else:
                # Process input stack validations
                validated_input = self._process_input_stack(lo.get("input_stack", {}), input_data)
                
                # Execute function if defined
                result = self._execute_function(lo, validated_input)
                
                # Apply output stack transformations
                result = self._process_output_stack(lo.get("output_stack", {}), result)
            
            # Log completion
            self._log_execution_event(
                instance_id, 
                "lo_complete", 
                go_id=go_id, 
                lo_id=lo_id, 
                details={"result": result}
            )
            
            return {
                "lo_id": lo_id,
                "status": "completed",
                "result": result
            }
            
        except Exception as e:
            self._log_execution_event(
                instance_id,
                "lo_error",
                go_id=go_id,
                lo_id=lo_id,
                details={"error": str(e)}
            )
            raise WorkflowExecutionException(
                f"Failed to execute Local Objective: {str(e)}",
                lo_id=lo_id,
                go_id=go_id
            )
    
    def _load_lo_definition(self, lo_id):
        """Load Local Objective definition."""
        # For now, simplified implementation
        lo = self.db.local_objectives.find_one({"lo_id": lo_id})
        if not lo:
            raise WorkflowExecutionException(f"Local Objective with ID {lo_id} not found")
        return lo
    
    def _execute_nested_los(self, instance_id, go_id, parent_lo, input_data=None):
        """Execute nested Local Objectives based on execution pathway."""
        pathway_type = parent_lo.get("execution_pathway", "sequential")
        nested_los = parent_lo.get("local_objectives", [])
        
        if pathway_type == "sequential":
            return self._execute_sequential_pathway(instance_id, go_id, parent_lo["lo_id"], nested_los, input_data)
        elif pathway_type == "parallel":
            return self._execute_parallel_pathway(instance_id, go_id, parent_lo["lo_id"], nested_los, input_data)
        elif pathway_type == "alternative":
            return self._execute_alternative_pathway(instance_id, go_id, parent_lo["lo_id"], nested_los, input_data)
        elif pathway_type == "recursive":
            return self._execute_recursive_pathway(instance_id, go_id, parent_lo["lo_id"], nested_los, input_data)
        else:
            raise WorkflowExecutionException(f"Unknown execution pathway type: {pathway_type}")
    
    def _execute_sequential_pathway(self, instance_id, go_id, parent_lo_id, nested_los, input_data):
        """Execute nested LOs sequentially, passing results from one to the next."""
        current_data = input_data.copy() if input_data else {}
        results = []
        
        for nested_lo in nested_los:
            # Execute nested LO
            lo_result = self.execute_local_objective(instance_id, go_id, nested_lo["lo_id"], current_data)
            results.append(lo_result)
            
            # Update current data for next LO
            if lo_result.get("result"):
                current_data.update(lo_result["result"])
        
        return {
            "parent_lo_id": parent_lo_id,
            "pathway_type": "sequential",
            "results": results,
            "final_state": current_data
        }
    
    def _execute_parallel_pathway(self, instance_id, go_id, parent_lo_id, nested_los, input_data):
        """Execute nested LOs in parallel, then combine results."""
        # In a real implementation, this would use async/await or threading
        # For now, we'll execute them sequentially but maintain parallel semantics
        results = []
        
        for nested_lo in nested_los:
            # Each LO gets the original input data
            lo_result = self.execute_local_objective(instance_id, go_id, nested_lo["lo_id"], input_data)
            results.append(lo_result)
        
        # Combine all results
        combined_results = {}
        for result in results:
            if result.get("result"):
                combined_results.update(result["result"])
        
        return {
            "parent_lo_id": parent_lo_id,
            "pathway_type": "parallel",
            "results": results,
            "final_state": combined_results
        }
    
    def _execute_alternative_pathway(self, instance_id, go_id, parent_lo_id, nested_los, input_data):
        """Execute one of several possible LOs based on conditions."""
        # Evaluate conditions to select the right LO
        selected_lo = self._evaluate_conditions(nested_los, input_data)
        
        if not selected_lo:
            raise WorkflowExecutionException("No alternative LO matched the conditions")
        
        # Execute the selected LO
        lo_result = self.execute_local_objective(instance_id, go_id, selected_lo["lo_id"], input_data)
        
        return {
            "parent_lo_id": parent_lo_id,
            "pathway_type": "alternative",
            "selected_lo_id": selected_lo["lo_id"],
            "result": lo_result
        }
    
    def _evaluate_conditions(self, nested_los, input_data):
        """Evaluate conditions to select the appropriate LO."""
        default_lo = None
    
        for lo in nested_los:
            if not lo.get("condition"):
                # Remember this as the default if no conditions match
                if default_lo is None:
                    default_lo = lo
                continue
            
            # Evaluate condition using the enhanced evaluator
            if self.condition_evaluator.evaluate(lo["condition"], input_data):
                return lo
    
        # Return default if no conditions matched
        return default_lo
    
    def _execute_recursive_pathway(self, instance_id, go_id, parent_lo_id, nested_los, input_data):
        """Execute LOs recursively based on a condition."""
        # For now, implement a basic recursive pattern with a maximum depth
        max_depth = 5  # Prevent infinite recursion
        depth = 0
        current_data = input_data.copy() if input_data else {}
        results = []
        
        while depth < max_depth:
            # Evaluate if we should continue recursion
            if depth > 0 and not self._should_continue_recursion(current_data):
                break
                
            # Find LO to execute in this iteration
            selected_lo = self._evaluate_conditions(nested_los, current_data)
            
            if not selected_lo:
                break
                
            # Execute the selected LO
            lo_result = self.execute_local_objective(instance_id, go_id, selected_lo["lo_id"], current_data)
            results.append(lo_result)
            
            # Update current data for next iteration
            if lo_result.get("result"):
                current_data.update(lo_result["result"])
                
            depth += 1
        
        return {
            "parent_lo_id": parent_lo_id,
            "pathway_type": "recursive",
            "iterations": depth,
            "results": results,
            "final_state": current_data
        }
    
    def _should_continue_recursion(self, data):
        """Determine if recursion should continue based on data state."""
        # In a real implementation, this would check a specific condition
        # For now, check if a "continue_recursion" flag is True
        return data.get("continue_recursion", False)
    
    def _process_input_stack(self, input_stack, input_data):
        """Process and validate input data based on input stack configuration."""
        if not input_stack:
            return input_data or {}
    
        # Apply data mapping if defined
        if "data_mapping" in input_stack:
            input_data = self.data_mapper.map_data(input_stack["data_mapping"], input_data)
    
        # Validate input data against requirements - PRESERVE EXISTING VALIDATION
        required_fields = input_stack.get("required_fields", [])
        for field in required_fields:
            if field not in input_data or input_data[field] is None or input_data[field] == "":
                raise WorkflowExecutionException(f"Required field '{field}' is missing or empty")
    
        # Apply existing validations if defined - PRESERVE EXISTING VALIDATION
        validations = input_stack.get("validations", {})
        for field, validation_rules in validations.items():
            if field in input_data:
                for rule in validation_rules:
                    validation_type = rule.get("type")
                
                    if validation_type == "type":
                        expected_type = rule.get("value")
                        if not self._validate_type(input_data[field], expected_type):
                            raise WorkflowExecutionException(
                                f"Field '{field}' has invalid type. Expected {expected_type}."
                            )
                    elif validation_type == "regex":
                        pattern = rule.get("pattern")
                        if not self._validate_regex(input_data[field], pattern):
                            raise WorkflowExecutionException(
                                f"Field '{field}' does not match required pattern."
                            )
                    elif validation_type == "range":
                        min_val = rule.get("min")
                        max_val = rule.get("max")
                        if not self._validate_range(input_data[field], min_val, max_val):
                            raise WorkflowExecutionException(
                                f"Field '{field}' is out of allowed range."
                            )
    
        # Add enhanced schema validation if defined

        try:
            if "schema" in input_stack:
                input_data = self.validator.validate(input_data, input_stack["schema"])
        except ValidationError as e:
            raise WorkflowExecutionException(
                f"Input validation error: {e.message}",
                details={"field": e.field, "error": e.message}
            )
    
        return input_data


    def _validate_type(self, value, expected_type):
        """Validate value is of expected type."""
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        return True

    def _validate_regex(self, value, pattern):
        """Validate value matches regex pattern."""
        import re
        if not isinstance(value, str):
            return False
        return bool(re.match(pattern, value))

    def _validate_range(self, value, min_val=None, max_val=None):
        """Validate value is within range."""
        if not isinstance(value, (int, float)):
            return False
    
        if min_val is not None and value < min_val:
            return False
        if max_val is not None and value > max_val:
            return False
    
        return True
    
    def _process_output_stack(self, output_stack, output_data):
        """Process and transform output data based on output stack configuration."""
        if not output_stack:
            return output_data or {}
    
        # Apply data mapping if defined
        if "data_mapping" in output_stack:
            output_data = self.data_mapper.map_data(output_stack["data_mapping"], output_data)
    
        # Apply transformations if defined - PRESERVE EXISTING FUNCTIONALITY
        transformations = output_stack.get("transformations", [])
        for transform in transformations:
            field = transform.get("field")
            transform_type = transform.get("type")
        
            if field in output_data:
                if transform_type == "round":
                    decimals = transform.get("decimals", 0)
                    output_data[field] = round(float(output_data[field]), decimals)
                elif transform_type == "format":
                    format_str = transform.get("format", "{}")
                    output_data[field] = format_str.format(output_data[field])
                elif transform_type == "truncate":
                    max_length = transform.get("max_length", 100)
                    if isinstance(output_data[field], str) and len(output_data[field]) > max_length:
                        output_data[field] = output_data[field][:max_length]
    
        # Add enhanced schema validation if defined
        try:
            if "schema" in output_stack:
                output_data = self.validator.validate(output_data, output_stack["schema"])
        except ValidationError as e:
            raise WorkflowExecutionException(
                f"Output validation error: {e.message}",
                details={"field": e.field, "error": e.message}
            )
    
        # Filter output fields if specified - PRESERVE EXISTING FUNCTIONALITY
        include_fields = output_stack.get("include_fields")
        if include_fields:
            output_data = {k: v for k, v in output_data.items() if k in include_fields}
    
        exclude_fields = output_stack.get("exclude_fields")
        if exclude_fields:
            output_data = {k: v for k, v in output_data.items() if k not in exclude_fields}
    
        return output_data


    def _execute_function(self, lo, input_data):
        """Execute the function defined for this LO."""
        function_spec = lo.get("function")
        if not function_spec:
            # No function defined, just pass through the data
            return input_data
    
        try:
            # Parse function specification
            if isinstance(function_spec, str):
                # Simple string format: "category.function_name"
                category, function_name = function_spec.split(".")
                args = []
                kwargs = input_data or {}
            else:
                # Dictionary format with explicit args/kwargs
                category = function_spec.get("category")
                function_name = function_spec.get("name")
                args = function_spec.get("args", [])
            
                # Extract kwargs from input data based on param_mapping
                kwargs = {}
                param_mapping = function_spec.get("param_mapping", {})
                for param_name, input_key in param_mapping.items():
                    if input_data and input_key in input_data:
                        kwargs[param_name] = input_data[input_key]
        
            # Execute the function from repository
            result = function_repository.execute(category, function_name, *args, **kwargs)
        
            # Wrap result if it's not already a dictionary
            if not isinstance(result, dict):
                result = {"result": result}
        
            return result

        except Exception as e:
                raise WorkflowExecutionException(
                f"Error executing function {function_spec}: {str(e)}",
                details={"original_error": str(e)}
            )


# Create a singleton workflow service
workflow_service = WorkflowService()
